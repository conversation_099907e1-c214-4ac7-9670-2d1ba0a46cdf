#!/usr/bin/env python3
# Quick Start Script for Social Media Blocking System
# سكريبت البدء السريع لنظام حظر وسائل التواصل الاجتماعي

import sys
import os
import time
from datetime import datetime

def print_banner():
    """Print system banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        نظام حظر وسائل التواصل الاجتماعي                      ║
    ║           Social Media Blocking System                       ║
    ║                                                              ║
    ║                    الإصدار 2.0.0                            ║
    ║                    Version 2.0.0                             ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    
    ⚠️  تحذير: هذا النظام مخصص للأغراض التعليمية والبحثية فقط
    ⚠️  Warning: This system is for educational and research purposes only
    """
    print(banner)

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 فحص المتطلبات - Checking Dependencies...")
    
    required_modules = [
        'requests',
        'beautifulsoup4',
        'selenium',
        'fake_useragent'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'beautifulsoup4':
                import bs4
            elif module == 'fake_useragent':
                import fake_useragent
            else:
                __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            print(f"  ❌ {module}")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ المتطلبات المفقودة - Missing Dependencies:")
        for module in missing_modules:
            print(f"   - {module}")
        
        print(f"\n📦 لتثبيت المتطلبات - To install dependencies:")
        print(f"   pip install {' '.join(missing_modules)}")
        return False
    
    print("✅ جميع المتطلبات متوفرة - All dependencies available")
    return True

def quick_demo():
    """Run a quick demonstration"""
    print("\n🚀 بدء العرض السريع - Starting Quick Demo...")
    
    try:
        # Import the system
        from social_media_blocking import SocialMediaBlockingFramework
        
        # Create mock bot
        class QuickBot:
            def __init__(self):
                self.bot_id = "quick_demo_bot"
            
            def send_data(self, data):
                print(f"📤 [BOT] Data sent: {data.get('operation_type', 'unknown')}")
        
        # Initialize framework
        bot = QuickBot()
        framework = SocialMediaBlockingFramework(bot)
        
        print("🔧 تهيئة النظام - Initializing System...")
        if framework.start_blocking_system():
            print("✅ تم بدء النظام بنجاح - System started successfully")
            
            # Show system status
            status = framework.get_blocking_system_status()
            print(f"\n📊 حالة النظام - System Status:")
            print(f"   🎯 المنصات المدعومة: {len(status['target_platforms'])}")
            print(f"   ⚙️  الاستراتيجيات: {len(status['blocking_strategies'])}")
            print(f"   📈 العمليات النشطة: {status['active_operations']}")
            
            # Quick test of mass reporting
            print(f"\n🧪 اختبار سريع - Quick Test...")
            if framework.blocking_engines['mass_reporting_engine']:
                config = {
                    'target_account': 'demo_target',
                    'target_platform': 'facebook',
                    'report_type': 'fake_account',
                    'reporter_count': 5
                }
                
                print(f"   📢 تشغيل بلاغات تجريبية...")
                campaign_id = framework.blocking_engines['mass_reporting_engine'].execute_mass_reporting_campaign(config)
                
                if campaign_id:
                    print(f"   ✅ تم بدء الحملة: {campaign_id}")
                    time.sleep(3)  # Wait a bit
                    
                    # Show updated stats
                    updated_status = framework.get_blocking_system_status()
                    stats = updated_status['blocking_statistics']
                    print(f"   📊 البلاغات المرسلة: {stats['reports_submitted']}")
                else:
                    print(f"   ❌ فشل في بدء الحملة")
            
            # Stop system
            print(f"\n🛑 إيقاف النظام - Stopping System...")
            framework.stop_blocking_system()
            print("✅ تم إيقاف النظام بنجاح - System stopped successfully")
            
        else:
            print("❌ فشل في بدء النظام - Failed to start system")
            
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد - Import Error: {e}")
        print("تأكد من وجود ملف social_media_blocking.py")
    except Exception as e:
        print(f"❌ خطأ في العرض - Demo Error: {e}")

def show_menu():
    """Show main menu"""
    menu = """
    📋 القائمة الرئيسية - Main Menu:

    1️⃣  تشغيل العرض السريع - Run Quick Demo
    2️⃣  تشغيل الاختبارات - Run Tests
    3️⃣  تشغيل الأمثلة - Run Examples
    4️⃣  عرض حالة النظام - Show System Status
    5️⃣  تثبيت المتطلبات - Install Dependencies
    6️⃣  عرض الوثائق - Show Documentation
    7️⃣  اختبار الهندسة الاجتماعية - Test Social Engineering
    8️⃣  تشغيل خادم التصيد - Start Phishing Server
    0️⃣  خروج - Exit

    """
    print(menu)

def install_dependencies():
    """Install required dependencies"""
    print("📦 تثبيت المتطلبات - Installing Dependencies...")
    
    try:
        import subprocess
        
        # Read requirements from file
        if os.path.exists('requirements.txt'):
            print("📄 قراءة requirements.txt...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ تم تثبيت المتطلبات بنجاح")
            else:
                print(f"❌ فشل في التثبيت: {result.stderr}")
        else:
            # Install basic requirements
            basic_requirements = [
                'requests>=2.28.0',
                'beautifulsoup4>=4.11.0', 
                'selenium>=4.0.0',
                'fake-useragent>=1.1.0'
            ]
            
            for req in basic_requirements:
                print(f"📦 تثبيت {req}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', req
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    print(f"  ✅ {req}")
                else:
                    print(f"  ❌ {req}: {result.stderr}")
                    
    except Exception as e:
        print(f"❌ خطأ في التثبيت: {e}")

def show_documentation():
    """Show basic documentation"""
    docs = """
    📚 الوثائق الأساسية - Basic Documentation:
    
    🎯 الهدف من النظام:
       - فهم آليات الحماية في منصات التواصل الاجتماعي
       - تطوير دفاعات أفضل ضد الهجمات
       - البحث الأكاديمي في أمان المنصات
    
    🔧 المكونات الرئيسية:
       - NetworkManager: إدارة الشبكة والبروكسي
       - MassReportingEngine: محرك البلاغات الجماعية
       - ContentViolationEngine: محرك انتهاك المحتوى
       - CopyrightStrikeEngine: محرك ضربات حقوق النشر
       - ImpersonationEngine: محرك انتحال الشخصية
    
    📋 الملفات المهمة:
       - social_media_blocking.py: الملف الرئيسي
       - config.py: ملف التكوين
       - test_blocking_system.py: ملف الاختبارات
       - example_usage.py: أمثلة الاستخدام
       - README_AR.md: الوثائق التفصيلية
    
    ⚠️  تحذير مهم:
       هذا النظام مخصص للأغراض التعليمية والبحثية فقط
       استخدامه لأغراض ضارة محظور ويمكن أن يؤدي لعواقب قانونية
    
    📞 للمساعدة:
       راجع ملف README_AR.md للوثائق التفصيلية
    """
    print(docs)

def test_social_engineering():
    """Test social engineering module"""
    print("🧪 اختبار وحدة الهندسة الاجتماعية...")

    try:
        # Check if test file exists
        if os.path.exists('test_social_engineering.py'):
            print("📄 تشغيل اختبارات الهندسة الاجتماعية...")
            os.system(f"{sys.executable} test_social_engineering.py")
        else:
            print("❌ ملف الاختبار غير موجود: test_social_engineering.py")

            # Try to run basic test
            try:
                from social_engineering import SocialEngineering

                class TestBot:
                    def __init__(self):
                        self.bot_id = "quick_test_bot"
                    def send_data(self, data):
                        print(f"📤 Test data: {data.get('type', 'unknown')}")

                bot = TestBot()
                se = SocialEngineering(bot)

                print("✅ تم إنشاء وحدة الهندسة الاجتماعية بنجاح")

                # Test basic functionality
                status = se.get_se_status()
                print(f"📊 التقنيات المتاحة: {sum(1 for v in se.se_techniques.values() if v)}")

            except ImportError:
                print("❌ لا يمكن استيراد وحدة الهندسة الاجتماعية")
            except Exception as e:
                print(f"❌ خطأ في الاختبار: {e}")

    except Exception as e:
        print(f"❌ خطأ في تشغيل الاختبارات: {e}")

def start_phishing_server():
    """Start phishing server"""
    print("🌐 بدء خادم التصيد...")

    try:
        from social_engineering import SocialEngineering

        class ServerBot:
            def __init__(self):
                self.bot_id = "phishing_server_bot"
            def send_data(self, data):
                print(f"📤 Server data: {data.get('type', 'unknown')}")

        bot = ServerBot()
        se = SocialEngineering(bot)

        # Get port from user
        try:
            port = input("🔢 أدخل المنفذ (افتراضي 8080): ").strip()
            if not port:
                port = 8080
            else:
                port = int(port)
        except ValueError:
            port = 8080
            print("⚠️  منفذ غير صحيح، استخدام المنفذ الافتراضي 8080")

        print(f"🚀 بدء خادم التصيد على المنفذ {port}...")

        # Start server
        success = se.start_phishing_server(port=port, host='127.0.0.1')

        if success:
            print(f"✅ تم بدء خادم التصيد بنجاح!")
            print(f"🌐 العناوين المتاحة:")
            print(f"   - http://127.0.0.1:{port}/microsoft")
            print(f"   - http://127.0.0.1:{port}/google")
            print(f"   - http://127.0.0.1:{port}/paypal")
            print(f"   - http://127.0.0.1:{port}/amazon")
            print(f"\n⚠️  تحذير: هذا الخادم للأغراض التعليمية فقط!")
            print(f"📝 اضغط Ctrl+C لإيقاف الخادم")

            try:
                # Keep server running
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print(f"\n🛑 تم إيقاف الخادم")
                se.stop_social_engineering()
        else:
            print("❌ فشل في بدء خادم التصيد")

    except ImportError:
        print("❌ لا يمكن استيراد وحدة الهندسة الاجتماعية")
    except Exception as e:
        print(f"❌ خطأ في بدء الخادم: {e}")

def main():
    """Main function"""
    print_banner()
    
    # Check dependencies first
    if not check_dependencies():
        choice = input("\n❓ هل تريد تثبيت المتطلبات؟ (y/n): ")
        if choice.lower() in ['y', 'yes', 'نعم']:
            install_dependencies()
            print("\n🔄 إعادة فحص المتطلبات...")
            if not check_dependencies():
                print("❌ لا يزال هناك متطلبات مفقودة")
                return
        else:
            print("❌ لا يمكن المتابعة بدون المتطلبات")
            return
    
    # Main loop
    while True:
        show_menu()
        
        try:
            choice = input("🔢 اختر رقماً - Choose a number: ").strip()
            
            if choice == '1':
                quick_demo()
            elif choice == '2':
                print("🧪 تشغيل الاختبارات...")
                os.system(f"{sys.executable} test_blocking_system.py")
            elif choice == '3':
                print("📝 تشغيل الأمثلة...")
                os.system(f"{sys.executable} example_usage.py")
            elif choice == '4':
                print("📊 عرض حالة النظام...")
                try:
                    from social_media_blocking import SocialMediaBlockingFramework
                    class StatusBot:
                        def __init__(self):
                            self.bot_id = "status_bot"
                        def send_data(self, data):
                            pass
                    
                    bot = StatusBot()
                    framework = SocialMediaBlockingFramework(bot)
                    framework.start_blocking_system()
                    status = framework.get_blocking_system_status()
                    
                    print(f"   🎯 المنصات: {len(status['target_platforms'])}")
                    print(f"   ⚙️  الاستراتيجيات: {len(status['blocking_strategies'])}")
                    print(f"   📈 العمليات: {status['active_operations']}")
                    
                    framework.stop_blocking_system()
                except Exception as e:
                    print(f"❌ خطأ: {e}")
            elif choice == '5':
                install_dependencies()
            elif choice == '6':
                show_documentation()
            elif choice == '7':
                test_social_engineering()
            elif choice == '8':
                start_phishing_server()
            elif choice == '0':
                print("👋 وداعاً - Goodbye!")
                break
            else:
                print("❌ اختيار غير صحيح - Invalid choice")
                
        except KeyboardInterrupt:
            print("\n\n👋 تم الإيقاف بواسطة المستخدم - Stopped by user")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")
        
        input("\n⏸️  اضغط Enter للمتابعة - Press Enter to continue...")

if __name__ == "__main__":
    main()
