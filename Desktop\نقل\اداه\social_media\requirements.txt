# Core dependencies for Social Media Blocking System
# المتطلبات الأساسية لنظام حظر وسائل التواصل الاجتماعي

# HTTP requests and web scraping
requests>=2.28.0
beautifulsoup4>=4.11.0
urllib3>=1.26.0

# Web automation
selenium>=4.0.0
webdriver-manager>=3.8.0

# Async HTTP
aiohttp>=3.8.0
asyncio-throttle>=1.0.0

# User agent spoofing
fake-useragent>=1.1.0

# Data processing and analysis
numpy>=1.21.0
pandas>=1.4.0

# Image processing and OCR
opencv-python>=4.6.0
Pillow>=9.0.0
pytesseract>=0.3.0

# Database
sqlite3  # Built-in with Python

# JSON and data handling
jsonschema>=4.0.0

# Networking and proxy
requests-toolbelt>=0.9.0
pysocks>=1.7.0

# Encryption and security
cryptography>=3.4.0

# Logging and monitoring
colorlog>=6.6.0

# Configuration management
python-dotenv>=0.19.0

# Rate limiting
ratelimit>=2.2.0

# Random data generation
faker>=15.0.0

# Time and date handling
python-dateutil>=2.8.0

# Social Engineering Module Requirements
# متطلبات وحدة الهندسة الاجتماعية

# Real phishing servers
flask>=2.2.0
werkzeug>=2.2.0

# SMS and Voice (Twilio)
twilio>=8.0.0

# DNS operations
dnspython>=2.2.0
python-whois>=0.8.0

# Phone number analysis
phonenumbers>=8.13.0

# Email handling
email-validator>=1.3.0

# Advanced networking
psutil>=5.9.0
scapy>=2.4.5

# Optional dependencies for advanced features
# متطلبات اختيارية للميزات المتقدمة

# Machine learning (optional)
# scikit-learn>=1.1.0
# tensorflow>=2.9.0

# Advanced image processing (optional)
# opencv-contrib-python>=4.6.0

# Advanced networking (optional)
# scapy>=2.4.0

# GUI (optional)
# tkinter  # Built-in with Python
# PyQt5>=5.15.0

# Development dependencies
# متطلبات التطوير

# Testing
pytest>=7.0.0
pytest-asyncio>=0.19.0

# Code formatting
black>=22.0.0
isort>=5.10.0

# Linting
flake8>=4.0.0
pylint>=2.14.0

# Type checking
mypy>=0.961
