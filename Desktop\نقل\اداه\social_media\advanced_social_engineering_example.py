#!/usr/bin/env python3
# Advanced Social Engineering Example
# مثال متقدم للهندسة الاجتماعية

import os
import sys
import time
import json
from datetime import datetime

# Import the social engineering module
try:
    from social_engineering import SocialEngineering
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("تأكد من وجود ملف social_engineering.py")
    sys.exit(1)

class AdvancedSEBot:
    """Advanced bot for social engineering operations"""
    def __init__(self):
        self.bot_id = "advanced_se_bot_001"
        self.name = "Advanced Social Engineering Bot"
        self.operations_log = []
    
    def send_data(self, data):
        """Log and process data"""
        self.operations_log.append({
            'timestamp': datetime.now().isoformat(),
            'data': data
        })
        print(f"📊 [ADVANCED BOT] {data.get('type', 'unknown')}: {data.get('target_email', data.get('target_phone', 'unknown'))}")

def print_banner():
    """Print advanced example banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║           مثال متقدم للهندسة الاجتماعية                     ║
    ║           Advanced Social Engineering Example                ║
    ║                                                              ║
    ║                    ⚠️ للأغراض التعليمية فقط ⚠️                ║
    ║                    ⚠️ Educational Purposes Only ⚠️            ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def setup_environment():
    """Setup environment variables for testing"""
    print("🔧 إعداد البيئة - Setting up environment...")
    
    # Check for environment variables
    required_vars = {
        'SMTP_SERVER': 'smtp.gmail.com',
        'SMTP_PORT': '587',
        'SMTP_USERNAME': '<EMAIL>',
        'SMTP_PASSWORD': 'your-app-password'
    }
    
    missing_vars = []
    for var, default in required_vars.items():
        if not os.getenv(var):
            missing_vars.append(var)
            print(f"⚠️  {var} not set, using default: {default}")
    
    if missing_vars:
        print(f"📝 لتفعيل الإرسال الحقيقي، قم بتعيين المتغيرات:")
        for var in missing_vars:
            print(f"   export {var}='your_value'")
    
    return len(missing_vars) == 0

def advanced_phishing_campaign():
    """Execute advanced phishing campaign"""
    print("\n" + "="*60)
    print("📧 حملة تصيد متقدمة - Advanced Phishing Campaign")
    print("="*60)
    
    # Initialize
    bot = AdvancedSEBot()
    se = SocialEngineering(bot)
    se.start_social_engineering()
    
    # Define targets with detailed profiles
    targets = [
        {
            'email': '<EMAIL>',
            'name': 'John Smith',
            'company': 'TechCorp',
            'position': 'CEO',
            'phone': '+1234567890',
            'interests': ['technology', 'business', 'golf'],
            'risk_level': 'high'
        },
        {
            'email': '<EMAIL>',
            'name': 'Sarah Johnson',
            'company': 'DataCorp',
            'position': 'IT Administrator',
            'phone': '+1234567891',
            'interests': ['cybersecurity', 'programming', 'gaming'],
            'risk_level': 'medium'
        },
        {
            'email': '<EMAIL>',
            'name': 'Mike Wilson',
            'company': 'FinanCorp',
            'position': 'Finance Manager',
            'phone': '+1234567892',
            'interests': ['finance', 'investing', 'sports'],
            'risk_level': 'low'
        }
    ]
    
    # Create campaign
    campaign_data = {
        'campaign_name': 'Advanced Corporate Phishing',
        'campaign_type': 'spear_phishing',
        'target_profile': 'corporate_executives',
        'psychological_technique': 'authority_urgency',
        'created_at': datetime.now().isoformat()
    }
    
    campaign_id = se.store_campaign(campaign_data)
    print(f"📋 تم إنشاء الحملة: {campaign_id}")
    
    # Process each target
    for i, target in enumerate(targets, 1):
        print(f"\n[{i}/{len(targets)}] معالجة الهدف: {target['name']}")
        
        # Psychological profiling
        profile = se.psychological_profiling(target)
        print(f"   🧠 الملف النفسي: {profile.get('risk_level', 'unknown')} risk")
        
        # Store target profile
        target_data = {
            'target_id': f"target_{i:03d}",
            'name': target['name'],
            'email': target['email'],
            'phone': target['phone'],
            'company': target['company'],
            'position': target['position'],
            'profiled_at': datetime.now().isoformat()
        }
        se.store_target_profile(target_data)
        
        # Select appropriate template based on profile
        if profile.get('risk_level') == 'high':
            template = 'urgent_security'
        elif 'authority' in profile.get('psychological_triggers', []):
            template = 'ceo_request'
        else:
            template = 'urgent_security'
        
        # Send phishing email
        print(f"   📧 إرسال بريد تصيد ({template})...")
        success = se.send_phishing_email(campaign_id, target, template)
        
        if success:
            print(f"   ✅ تم الإرسال بنجاح")
        else:
            print(f"   ❌ فشل في الإرسال")
        
        # Simulate delay between targets
        time.sleep(2)
    
    # Campaign summary
    print(f"\n📊 ملخص الحملة:")
    print(f"   🎯 الأهداف: {len(targets)}")
    print(f"   📧 الرسائل المرسلة: {len([t for t in targets])}")
    print(f"   📈 البيانات المسجلة: {len(bot.operations_log)}")
    
    se.stop_social_engineering()
    return campaign_id

def multi_vector_attack():
    """Execute multi-vector social engineering attack"""
    print("\n" + "="*60)
    print("🎯 هجوم متعدد المسارات - Multi-Vector Attack")
    print("="*60)
    
    bot = AdvancedSEBot()
    se = SocialEngineering(bot)
    se.start_social_engineering()
    
    # High-value target
    target = {
        'email': '<EMAIL>',
        'name': 'Alex Thompson',
        'company': 'SecureCorp',
        'position': 'CTO',
        'phone': '+1555123456',
        'linkedin': 'alex-thompson-cto',
        'interests': ['cybersecurity', 'AI', 'blockchain']
    }
    
    print(f"🎯 الهدف: {target['name']} ({target['position']})")
    
    # Phase 1: OSINT Gathering
    print(f"\n📊 المرحلة 1: جمع المعلومات الاستخباراتية")
    se.email_reconnaissance(target['company'].lower() + '.com')
    se.social_media_reconnaissance(['LinkedIn', 'Twitter'])
    se.company_reconnaissance([target['company']])
    
    # Phase 2: Psychological Profiling
    print(f"\n🧠 المرحلة 2: التحليل النفسي")
    profile = se.psychological_profiling(target)
    print(f"   مستوى المخاطر: {profile.get('risk_level', 'unknown')}")
    print(f"   نقاط الضعف: {len(profile.get('vulnerabilities', []))}")
    
    # Phase 3: Multi-Channel Attack
    print(f"\n⚔️  المرحلة 3: الهجوم متعدد القنوات")
    
    # Email phishing
    print(f"   📧 1. تصيد البريد الإلكتروني...")
    email_success = se.send_phishing_email('multi_vector_001', target, 'urgent_security')
    
    # Vishing call
    print(f"   📞 2. مكالمة Vishing...")
    vishing_script = {'type': 'it_support', 'urgency': 'critical'}
    vishing_success = se.execute_vishing_call(target, vishing_script)
    
    # Smishing SMS
    print(f"   📱 3. رسالة Smishing...")
    smishing_success = se.send_smishing_sms(target, 'security_alert')
    
    # Phase 4: Results Analysis
    print(f"\n📈 المرحلة 4: تحليل النتائج")
    success_count = sum([email_success, vishing_success, smishing_success])
    print(f"   نجح: {success_count}/3 هجمات")
    print(f"   معدل النجاح: {(success_count/3)*100:.1f}%")
    
    # Store results
    attack_data = {
        'target_name': target['name'],
        'attack_vectors': ['email', 'vishing', 'smishing'],
        'success_count': success_count,
        'timestamp': datetime.now().isoformat()
    }
    
    se.stop_social_engineering()
    return attack_data

def real_phishing_server_demo():
    """Demonstrate real phishing server"""
    print("\n" + "="*60)
    print("🌐 عرض خادم التصيد الحقيقي - Real Phishing Server Demo")
    print("="*60)
    
    bot = AdvancedSEBot()
    se = SocialEngineering(bot)
    
    # Start phishing server
    print("🚀 بدء خادم التصيد...")
    success = se.start_phishing_server(port=8082, host='127.0.0.1')
    
    if success:
        print("✅ تم بدء الخادم بنجاح!")
        print("\n🌐 الصفحات المتاحة:")
        print("   📍 Microsoft: http://127.0.0.1:8082/microsoft")
        print("   📍 Google: http://127.0.0.1:8082/google")
        print("   📍 PayPal: http://127.0.0.1:8082/paypal")
        print("   📍 Amazon: http://127.0.0.1:8082/amazon")
        
        print("\n⚠️  تحذير: هذا الخادم للأغراض التعليمية فقط!")
        print("📝 افتح المتصفح وانتقل إلى أحد العناوين أعلاه")
        print("🔍 راقب وحدة التحكم لرؤية البيانات المجمعة")
        print("⏹️  اضغط Ctrl+C لإيقاف الخادم")
        
        try:
            # Monitor server
            start_time = time.time()
            while True:
                time.sleep(5)
                
                # Show periodic status
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0:  # Every 30 seconds
                    print(f"⏰ الخادم يعمل منذ {elapsed} ثانية...")
                    
        except KeyboardInterrupt:
            print(f"\n🛑 إيقاف الخادم...")
            se.stop_social_engineering()
            print("✅ تم إيقاف الخادم بنجاح")
    else:
        print("❌ فشل في بدء الخادم")

def comprehensive_test():
    """Run comprehensive test of all features"""
    print("\n" + "="*60)
    print("🧪 اختبار شامل - Comprehensive Test")
    print("="*60)
    
    bot = AdvancedSEBot()
    se = SocialEngineering(bot)
    
    # Test all major components
    tests = [
        ("Email Templates", lambda: len(se.load_email_templates()) > 0),
        ("Phishing Domains", lambda: len(se.generate_phishing_domains()) > 0),
        ("Fake Profiles", lambda: len(se.create_fake_profiles()) > 0),
        ("Database Init", lambda: se.init_se_db()),
        ("System Status", lambda: se.get_se_status() is not None),
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = bool(result)
            status = "✅" if result else "❌"
            print(f"   {status} {test_name}")
        except Exception as e:
            results[test_name] = False
            print(f"   ❌ {test_name}: {e}")
    
    # Summary
    passed = sum(results.values())
    total = len(results)
    print(f"\n📊 النتائج: {passed}/{total} اختبارات نجحت ({(passed/total)*100:.1f}%)")
    
    return results

def main():
    """Main function"""
    print_banner()
    
    # Setup
    env_ready = setup_environment()
    
    # Menu
    while True:
        print("\n" + "="*60)
        print("📋 القائمة الرئيسية - Main Menu")
        print("="*60)
        print("1. 📧 حملة تصيد متقدمة - Advanced Phishing Campaign")
        print("2. 🎯 هجوم متعدد المسارات - Multi-Vector Attack")
        print("3. 🌐 عرض خادم التصيد - Phishing Server Demo")
        print("4. 🧪 اختبار شامل - Comprehensive Test")
        print("0. 🚪 خروج - Exit")
        
        choice = input("\n🔢 اختر رقماً: ").strip()
        
        try:
            if choice == '1':
                advanced_phishing_campaign()
            elif choice == '2':
                multi_vector_attack()
            elif choice == '3':
                real_phishing_server_demo()
            elif choice == '4':
                comprehensive_test()
            elif choice == '0':
                print("👋 وداعاً!")
                break
            else:
                print("❌ اختيار غير صحيح")
                
        except KeyboardInterrupt:
            print("\n⏹️  تم إيقاف العملية")
        except Exception as e:
            print(f"\n❌ خطأ: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
